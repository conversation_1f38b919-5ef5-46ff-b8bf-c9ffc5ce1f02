# 🧪 EXEMPLE DE TESTARE PENTRU SCHIMBAREA DE STATUS

## Payload-uri care vor fi trimise către <PERSON>

### 1. Status simplu (ex: "New Lead", "Contacted", etc.)
```json
{
  "statusId": 1,
  "remark": "Status changed to new lead"
}
```

### 2. Meeting Scheduled
```json
{
  "statusId": 2,
  "reminder": {
    "title": "Meeting appointment",
    "content": "Meeting appointment reminder.",
    "due_date": "2024-01-15",
    "priority": "medium",
    "remarks": "Important client meeting",
    "send_email": true,
    "send_reminder_date": "2024-01-15"
  }
}
```

### 3. Viewing Scheduled
```json
{
  "statusId": 3,
  "listing_ids": [101, 102, 103],
  "reminder": {
    "title": "Viewing appointment",
    "content": "Property viewing reminder.",
    "due_date": "2024-01-20",
    "priority": "high",
    "remarks": "Show premium properties",
    "send_email": true,
    "send_reminder_date": "2024-01-20"
  }
}
```

### 4. Follow Up
```json
{
  "statusId": 4,
  "reminder": {
    "title": "Follow up",
    "content": "Follow up reminder.",
    "due_date": "2024-01-25",
    "priority": "low",
    "remarks": "Check client interest",
    "send_email": false,
    "send_reminder_date": "2024-01-25"
  }
}
```

## 🔍 Ce să verifici în Laravel logs

După ce corectezi codul Laravel, verifică în logs:

1. **Request-ul primit:**
   ```
   [timestamp] Request received: PATCH /v2/leads/123/statusChange
   [timestamp] Request data: {"statusId": 2, "reminder": {...}}
   ```

2. **Validarea:**
   ```
   [timestamp] Validation passed for status change
   ```

3. **Reminder creat:**
   ```
   [timestamp] Reminder created for lead #123
   ```

4. **Status actualizat:**
   ```
   [timestamp] Status updated from [Old Status] to [New Status] by User Name
   ```

## 🚀 Pașii pentru testare

1. **Corectează codul Laravel** cu fix-ul din `laravel-fix-needed.md`
2. **Testează din aplicația mobilă** schimbarea de status
3. **Verifică logs-urile** pentru a vedea că totul funcționează
4. **Testează diferite tipuri de statusuri:**
   - Status simplu (doar cu remark)
   - Meeting Scheduled (cu reminder)
   - Viewing Scheduled (cu reminder + properties)
   - Follow Up (cu reminder)

## ✅ Semnele că funcționează corect

- ✅ Nu mai primești 403 Forbidden
- ✅ Status-ul se schimbă în baza de date
- ✅ Se creează reminder-ul pentru statusurile complexe
- ✅ Se adaugă în operation history
- ✅ Aplicația se întoarce la ecranul anterior

## 🐛 Debugging suplimentar

Dacă încă ai probleme, adaugă în Laravel controller:

```php
Log::info('Status change request received', [
    'leadId' => $leadId,
    'userId' => auth()->user()->id,
    'requestData' => request()->all()
]);
```

Acest log te va ajuta să vezi exact ce date primește Laravel-ul de la aplicația mobilă.
