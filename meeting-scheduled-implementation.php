<?php

// Înlocuiește funcția statusChange cu această versiune completă:

public function statusChange($leadId)
{
    $lead = Lead::find($leadId);
    
    if (is_null($lead)) {
        abort(404, 'Lead not found');
    }
    
    $agent = $lead->latestAssignment->user;
    if ($agent->id !== auth()->user()->id && !auth()->user()->hasRole(RolesDef::OFFICE_MANAGER)) {
        abort(403, 'You are not authorized to change this lead status');
    }
    
    $newLeadStatusId = request()->get('statusId');
    $statusesMap = $this->leadStatusService->getLeadStatusesMap();
    
    if ($newLeadStatusId == $statusesMap[LeadStatus::STATUS_MEETING_SCHEDULED]) {
        $createReminder = true;
        $arrayToValidate = [
            'reminder.title' => ['nullable', 'string', 'max:255'],
            'reminder.content' => ['nullable', 'string'],
            'reminder.due_date' => ['nullable', 'date', 'after_or_equal:today'],
            'reminder.priority' => ['nullable', 'in:low,medium,high'],
            'reminder.remarks' => ['nullable', 'string'],
            'reminder.send_email' => ['nullable', 'boolean'],
            'reminder.send_reminder_date' => ['nullable', 'date', 'before_or_equal:reminder.due_date'],
        ];
    } elseif ($newLeadStatusId == $statusesMap[LeadStatus::STATUS_VIEWING_SCHEDULED]) {
        $createReminder = true;
        $arrayToValidate = [
            'listing_ids' => ['required', 'array', 'min:1'],
            'listing_ids.*' => ['integer', 'exists:listings,id'],
            'reminder.title' => ['nullable', 'string', 'max:255'],
            'reminder.content' => ['nullable', 'string'],
            'reminder.due_date' => ['nullable', 'date', 'after_or_equal:today'],
            'reminder.priority' => ['nullable', 'in:low,medium,high'],
            'reminder.remarks' => ['nullable', 'string'],
            'reminder.send_email' => ['nullable', 'boolean'],
            'reminder.send_reminder_date' => ['nullable', 'date', 'before_or_equal:reminder.due_date'],
        ];
    } elseif ($newLeadStatusId == $statusesMap[LeadStatus::STATUS_FOLLOW_UP]) {
        $createReminder = true;
        $arrayToValidate = [
            'reminder.title' => ['nullable', 'string', 'max:255'],
            'reminder.content' => ['nullable', 'string'],
            'reminder.due_date' => ['nullable', 'date', 'after_or_equal:today'],
            'reminder.priority' => ['nullable', 'in:low,medium,high'],
            'reminder.remarks' => ['nullable', 'string'],
            'reminder.send_email' => ['nullable', 'boolean'],
            'reminder.send_reminder_date' => ['nullable', 'date', 'before_or_equal:reminder.due_date'],
        ];
    } else {
        $createReminder = false;
        $arrayToValidate = ['remark' => 'required'];
    }
    
    $validData = request()->validate($arrayToValidate);
    
    try {
        DB::beginTransaction();
        
        // Creează reminder-ul dacă e necesar
        $reminder = null;
        if ($createReminder) {
            $reminderData = $validData['reminder'] ?? [];
            $reminderData['object_type'] = 'leads';
            $reminderData['object_id'] = $lead->id;
            $reminder = Reminder::create($reminderData);
            Log::info('Reminder created for lead #' . $lead->id . ' with ID: ' . $reminder->id);
        }
        
        // Pentru MEETING_SCHEDULED, salvează detaliile meeting-ului
        if ($newLeadStatusId == $statusesMap[LeadStatus::STATUS_MEETING_SCHEDULED]) {
            // Opțiunea 1: Salvează în tabela separată lead_meetings
            LeadMeeting::updateOrCreate(
                ['lead_id' => $lead->id],
                [
                    'scheduled_at' => isset($validData['reminder']['due_date']) 
                        ? Carbon::parse($validData['reminder']['due_date']) 
                        : now()->addDay(),
                    'title' => $validData['reminder']['title'] ?? 'Meeting appointment',
                    'content' => $validData['reminder']['content'] ?? 'Meeting appointment reminder.',
                    'priority' => $validData['reminder']['priority'] ?? 'medium',
                    'notes' => $validData['reminder']['remarks'] ?? null,
                    'send_email' => $validData['reminder']['send_email'] ?? false,
                    'status' => 'scheduled',
                    'reminder_id' => $reminder?->id,
                ]
            );
            
            Log::info('Meeting scheduled for lead #' . $lead->id . ' at ' . ($validData['reminder']['due_date'] ?? 'tomorrow'));
        }
        
        // Pentru VIEWING_SCHEDULED, gestionează programările
        if ($newLeadStatusId == $statusesMap[LeadStatus::STATUS_VIEWING_SCHEDULED] && isset($validData['listing_ids'])) {
            // Șterge programările anterioare
            LeadViewingSchedule::where('lead_id', $lead->id)->delete();
            
            // Creează noile programări
            foreach ($validData['listing_ids'] as $listingId) {
                LeadViewingSchedule::create([
                    'lead_id' => $lead->id,
                    'listing_id' => $listingId,
                    'scheduled_at' => isset($validData['reminder']['due_date']) 
                        ? Carbon::parse($validData['reminder']['due_date']) 
                        : now()->addDay(),
                    'status' => 'scheduled',
                    'notes' => $validData['reminder']['remarks'] ?? null,
                    'reminder_id' => $reminder?->id,
                ]);
            }
            
            Log::info('Viewing scheduled for lead #' . $lead->id . ' with ' . count($validData['listing_ids']) . ' properties');
        }

        // Actualizează statusul lead-ului
        $newStatus = $this->leadStatusService->getLeadStatuses()->firstWhere('id', $newLeadStatusId);
        if (is_null($newStatus)) {
            throw new \Exception('Invalid status ID');
        }
        
        $newStatusLabel = $newStatus->name;
        $previousStatus = $lead->leadStatus;
        $oldStatusLabel = 'NO STATUS';
        if (!is_null($previousStatus)) {
            $oldStatusLabel = $previousStatus->name;
        }

        $this->leadsService->trackLeadStatusChange($lead, $newStatus);

        $lead->lead_status_id = $newStatus->id;
        $lead->save();

        // Creează mesajul pentru operation history
        $remarksBody = "Lead status updated from [" . $oldStatusLabel . "] to [" . $newStatusLabel . "]";
        
        // Adaugă informații specifice pentru fiecare tip de status
        if ($newLeadStatusId == $statusesMap[LeadStatus::STATUS_MEETING_SCHEDULED]) {
            $meetingDate = $validData['reminder']['due_date'] ?? 'tomorrow';
            $remarksBody .= " with meeting scheduled for " . $meetingDate;
        } elseif ($newLeadStatusId == $statusesMap[LeadStatus::STATUS_VIEWING_SCHEDULED] && isset($validData['listing_ids'])) {
            $remarksBody .= " with " . count($validData['listing_ids']) . " properties scheduled for viewing";
        } elseif ($newLeadStatusId == $statusesMap[LeadStatus::STATUS_FOLLOW_UP]) {
            $remarksBody .= " with follow-up scheduled";
        }
        
        Log::info($remarksBody . ' by ' . auth()->user()->name);
        $this->operationHistoryService->addOperationHistory($lead, $remarksBody, auth()->user());
        
        // Adaugă remark-ul utilizatorului dacă există
        if (array_key_exists('remark', $validData)) {
            $this->operationHistoryService->addOperationHistory($lead, $validData['remark'], auth()->user());
        }
        
        $lead->load(['leadStatus', 'meetings', 'viewingSchedules.listing']);
        
        DB::commit();
        
        // Returnează răspuns de succes
        return response()->json([
            'success' => true,
            'message' => 'Lead status updated successfully',
            'lead' => $lead,
            'status' => $newStatus,
            'reminder_created' => $createReminder,
            'reminder_id' => $reminder?->id,
            'meeting_scheduled' => $newLeadStatusId == $statusesMap[LeadStatus::STATUS_MEETING_SCHEDULED],
            'properties_scheduled' => isset($validData['listing_ids']) ? count($validData['listing_ids']) : 0,
            'meetings' => $lead->meetings ?? [],
            'viewing_schedules' => $lead->viewingSchedules ?? []
        ]);
        
    } catch (\Exception $e) {
        DB::rollBack();
        Log::error('Error updating lead status: ' . $e->getMessage(), [
            'lead_id' => $leadId,
            'status_id' => $newLeadStatusId,
            'user_id' => auth()->user()->id,
            'error' => $e->getTraceAsString()
        ]);
        
        return response()->json([
            'success' => false,
            'message' => 'Failed to update lead status: ' . $e->getMessage()
        ], 500);
    }
}

// Nu uita să adaugi la începutul controller-ului:
// use App\Models\LeadMeeting;
// use App\Models\LeadViewingSchedule;
// use Illuminate\Support\Facades\DB;
// use Carbon\Carbon;
