<?php

// app/Models/LeadMeeting.php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Carbon\Carbon;

class LeadMeeting extends Model
{
    use HasFactory;

    protected $fillable = [
        'lead_id',
        'reminder_id',
        'scheduled_at',
        'title',
        'content',
        'priority',
        'notes',
        'send_email',
        'status',
        'completed_at',
        'outcome',
    ];

    protected $casts = [
        'scheduled_at' => 'datetime',
        'completed_at' => 'datetime',
        'send_email' => 'boolean',
    ];

    /**
     * Get the lead that owns the meeting.
     */
    public function lead(): BelongsTo
    {
        return $this->belongsTo(Lead::class);
    }

    /**
     * Get the reminder associated with this meeting.
     */
    public function reminder(): BelongsTo
    {
        return $this->belongsTo(Reminder::class);
    }

    /**
     * Scope pentru meeting-uri programate
     */
    public function scopeScheduled($query)
    {
        return $query->where('status', 'scheduled');
    }

    /**
     * Scope pentru meeting-uri completate
     */
    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    /**
     * Scope pentru meeting-uri anulate
     */
    public function scopeCancelled($query)
    {
        return $query->where('status', 'cancelled');
    }

    /**
     * Scope pentru meeting-uri din ziua curentă
     */
    public function scopeToday($query)
    {
        return $query->whereDate('scheduled_at', Carbon::today());
    }

    /**
     * Scope pentru meeting-uri din săptămâna curentă
     */
    public function scopeThisWeek($query)
    {
        return $query->whereBetween('scheduled_at', [
            Carbon::now()->startOfWeek(),
            Carbon::now()->endOfWeek()
        ]);
    }

    /**
     * Scope pentru meeting-uri viitoare
     */
    public function scopeUpcoming($query)
    {
        return $query->where('scheduled_at', '>', Carbon::now())
                    ->where('status', 'scheduled');
    }

    /**
     * Scope pentru meeting-uri trecute
     */
    public function scopePast($query)
    {
        return $query->where('scheduled_at', '<', Carbon::now());
    }

    /**
     * Marchează meeting-ul ca fiind completat
     */
    public function markAsCompleted($outcome = null)
    {
        $this->update([
            'status' => 'completed',
            'completed_at' => Carbon::now(),
            'outcome' => $outcome
        ]);
    }

    /**
     * Anulează meeting-ul
     */
    public function cancel()
    {
        $this->update(['status' => 'cancelled']);
    }

    /**
     * Reprogramează meeting-ul
     */
    public function reschedule(Carbon $newDateTime)
    {
        $this->update([
            'scheduled_at' => $newDateTime,
            'status' => 'rescheduled'
        ]);
    }

    /**
     * Verifică dacă meeting-ul este în următoarele 24 de ore
     */
    public function isUpcomingSoon()
    {
        return $this->scheduled_at && 
               $this->scheduled_at->isBetween(Carbon::now(), Carbon::now()->addDay()) &&
               $this->status === 'scheduled';
    }

    /**
     * Formatează data pentru afișare
     */
    public function getFormattedScheduledAtAttribute()
    {
        return $this->scheduled_at ? $this->scheduled_at->format('d/m/Y H:i') : null;
    }
}

// Adaugă și în modelul Lead.php aceste relații:

/*
// În app/Models/Lead.php

public function meetings()
{
    return $this->hasMany(LeadMeeting::class);
}

public function scheduledMeetings()
{
    return $this->meetings()->scheduled();
}

public function upcomingMeetings()
{
    return $this->meetings()->upcoming();
}

public function latestMeeting()
{
    return $this->hasOne(LeadMeeting::class)->latest('scheduled_at');
}
*/
