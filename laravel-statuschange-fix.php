<?php

// Înlocuiește funcția statusChange cu această versiune care apelează direct updateLeadStatus:

public function statusChange($leadId)
{
    $lead = Lead::find($leadId);
    $agent = $lead->latestAssignment->user;
    
    if (is_null($lead) || ($agent->id !== auth()->user()->id && !auth()->user()->hasRole(RolesDef::OFFICE_MANAGER))) {
        abort(403, 'You are not authorized to change this lead status');
    }
    
    // În loc să faci HTTP call, apelează direct updateLeadStatus
    // Setează request-ul cu datele necesare
    request()->merge([
        'agent' => $agent->id,
        'status' => request()->get('status'),
        'remarks' => request()->get('remarks'),
        'viewingScheduledData' => request()->get('viewingScheduledData'),
    ]);
    
    // Apelează direct funcția updateLeadStatus
    return $this->updateLeadStatus($leadId);
}

// ALTERNATIV: Dacă updateLeadStatus este într-un alt controller, po<PERSON><PERSON> face așa:

public function statusChange($leadId)
{
    $lead = Lead::find($leadId);
    $agent = $lead->latestAssignment->user;
    
    if (is_null($lead) || ($agent->id !== auth()->user()->id && !auth()->user()->hasRole(RolesDef::OFFICE_MANAGER))) {
        abort(403, 'You are not authorized to change this lead status');
    }
    
    // Creează un nou request cu datele necesare
    $requestData = [
        'agent' => $agent->id,
        'status' => request()->get('status'),
        'remarks' => request()->get('remarks'),
        'viewingScheduledData' => request()->get('viewingScheduledData'),
    ];
    
    // Setează datele în request
    foreach ($requestData as $key => $value) {
        request()->merge([$key => $value]);
    }
    
    // Apelează direct funcția updateLeadStatus
    return $this->updateLeadStatus($leadId);
}

// SAU, dacă vrei să păstrezi logica separată, poți face așa:

public function statusChange($leadId)
{
    $lead = Lead::find($leadId);
    $agent = $lead->latestAssignment->user;
    
    if (is_null($lead) || ($agent->id !== auth()->user()->id && !auth()->user()->hasRole(RolesDef::OFFICE_MANAGER))) {
        abort(403, 'You are not authorized to change this lead status');
    }
    
    try {
        // Pregătește datele pentru updateLeadStatus
        $updateData = [
            'agent' => $agent->id,
            'status' => request()->get('status'),
            'remarks' => request()->get('remarks'),
            'viewingScheduledData' => request()->get('viewingScheduledData'),
        ];
        
        // Validează datele
        $validData = validator($updateData, [
            'agent' => 'required_if:status,21',
            'status' => 'exists:lead_status,id',
            'remarks' => 'required_unless:status,14,15,23,26',
            'viewingScheduledData' => 'required_if:status,14',
        ])->validate();
        
        // Apelează logica din updateLeadStatus direct aici
        // (copiază codul din updateLeadStatus și adaptează-l)
        
        $oldStatusLabel = '';
        $newStatusLabel = '';
        $reminderRemarks = '';

        $lead = Lead::where('id', $leadId)->with('latestAssignment')->firstOrFail();
        $allStatuses = $this->leadStatusService->getLeadStatuses();
        $leadStatus = LeadStatus::where('id', $validData['status'])->first();
        
        if ($validData['status'] == '26') {
            $agent = auth()->user();
            $today = date('Y-m-d');
            $afterTreeMonths = date('Y-m-d', strtotime('+3 months', strtotime($today)));
            $reminderData = [
                'title' => 'Far moving decision',
                'text' => 'Far moving date on ' . $agent->name . ' by ' . $today,
                'priority' => 'low',
                'due_date' => $afterTreeMonths,
                'reminder_email' => false,
                'reminder_email_date' => null,
                'reminder_type' => $leadStatus->name,
            ];
            $this->notesService->createReminderForLead($lead, $reminderData);
        }

        if ($validData['status'] == '14' || $validData['status'] == '15' || $validData['status'] == '23' || $validData['status'] == '16') {
            // reminder
            if (isset($validData['viewingScheduledData']['reminder'])) {
                $reminderData = [
                    'title' => $validData['viewingScheduledData']['reminder']['title'],
                    'text' => $validData['viewingScheduledData']['reminder']['content'],
                    'priority' => $validData['viewingScheduledData']['reminder']['priority'],
                    'due_date' => $validData['viewingScheduledData']['reminder']['dueDate'],
                    'reminder_email' => $validData['viewingScheduledData']['reminder']['sendEmail'],
                    'reminder_email_date' => $validData['viewingScheduledData']['reminder']['sendReminderDate'],
                    'reminder_type' => $leadStatus->name,
                ];
                $newLeadStatus = $allStatuses->where('id', $validData['status'])->first();
                $newLeadStatusLabel = null;
                if (!is_null($newLeadStatus)) {
                    $newLeadStatusLabel = $newLeadStatus->name;
                }
                $shouldSetTask = $newLeadStatusLabel == 'VIEWING_SCHEDULED' || $newLeadStatusLabel == 'MEETING_SCHEDULED';
                $taskData = null;
                if ($shouldSetTask) {
                    $taskData = [
                        'status' => $newLeadStatusLabel,
                        'listingSelection' => $validData['viewingScheduledData']['listingSelection'],
                    ];
                }
                $this->notesService->createReminderForLead($lead, $reminderData, $taskData);
            }
            
            // proposals
            if (isset($validData['viewingScheduledData']['listingSelection'])) {
                foreach ($validData['viewingScheduledData']['listingSelection'] as $listingId => $listingRefNo) {
                    $listingAlreadyProposed = false;
                    foreach ($lead->proposals as $proposal) {
                        if ($proposal->property->id == $listingId) {
                            $listingAlreadyProposed = true;
                        }
                    }
                    if (!$listingAlreadyProposed) {
                        $newProposal = new LeadProposal();
                        $newProposal->lead_id = $lead->id;
                        $newProposal->property_id = $listingId;
                        $newProposal->save();
                    }
                }
            }
            $reminderRemarks = $validData['viewingScheduledData']['remarks'] ?? '';
        }

        $previousStatus = $lead->leadStatus;
        if (!is_null($previousStatus)) {
            $oldStatusLabel = $previousStatus->name;
        }
        $newStatus = $allStatuses->where('id', $validData['status'])->first();
        if (!is_null($newStatus)) {
            $newStatusLabel = $newStatus->name;
        }

        $this->leadsService->trackLeadStatusChange($lead, $leadStatus);
        $lead->lead_status_id = $validData['status'];
        $lead->save();

        $remarksBody = "Lead status updated from [" . $oldStatusLabel . "] to [" . $newStatusLabel . "] ";
        if (!empty($validData['remarks'])) {
            $remarksBody .= "\r\nRemarks:\r\n" . $validData['remarks'];
        }
        if (!empty($reminderRemarks)) {
            $remarksBody .= "\r\nReminder remarks:\r\n " . $reminderRemarks;
        }

        $this->operationHistoryService->addOperationHistory($lead, $remarksBody, auth()->user());

        // Assignment logic
        if ($validData['status'] == '21') {
            $user = User::where('id', $validData['agent'])->first();
            $this->leadsService->assignLeadToUser($lead, $user);
        } elseif (is_null($lead->latestAssignment)) {
            $this->leadsService->assignLeadToUser($lead, auth()->user());
        } else {
            $currentUser = auth()->user();
            $currentUserIsAgent = $currentUser->hasRole(RolesDef::AGENT);
            if ($currentUserIsAgent) {
                $currentAssignment = $lead->latestAssignment;
                if (!is_null($currentAssignment) && $currentAssignment->user_id != $currentUser->id) {
                    $this->leadsService->assignLeadToUser($lead, $currentUser);
                }
            }
        }

        if ($validData['status'] == '14') {
            // Email notifications for viewing scheduled
            if (isset($validData['viewingScheduledData']['listingSelection'])) {
                $properties = Property::with('contact', 'author')
                    ->whereIn('ref_no', array_values($validData['viewingScheduledData']['listingSelection']))
                    ->get();

                foreach ($properties as $property) {
                    if (!is_null($property->contact->email_1) || !is_null($property->contact->email_2)) {
                        $this->emailService->sendPropertyVisitOwnerNotification($property, $lead, $reminderData);
                    }

                    if (!is_null($lead->contact->email_1) || !is_null($lead->contact->email_2)) {
                        $this->emailService->sendPropertyVisitClientNotification($property, $lead, $reminderData);
                    }
                }
            }
        }

        $action = LeadInteractionTracking::UPDATE_STATUS;
        $this->authoServ->leadInteractionsTracking($lead->id, $action, null);

        return response()->json([
            "success" => true,
            "message" => "Lead status updated successfully",
            "lead" => $lead->load('leadStatus')
        ]);
        
    } catch (\Exception $ex) {
        return response()->json([
            'success' => false,
            'message' => $ex->getMessage()
        ], 500);
    }
}
