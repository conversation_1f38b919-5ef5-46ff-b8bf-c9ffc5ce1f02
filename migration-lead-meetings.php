<?php

// Creează această migrație pentru a stoca programările de meeting
// php artisan make:migration create_lead_meetings_table

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('lead_meetings', function (Blueprint $table) {
            $table->id();
            $table->foreignId('lead_id')->constrained('leads')->onDelete('cascade');
            $table->foreignId('reminder_id')->nullable()->constrained('reminders')->onDelete('set null');
            $table->timestamp('scheduled_at')->nullable(); // Când e programat meeting-ul
            $table->string('title')->nullable(); // Titlul meeting-ului
            $table->text('content')->nullable(); // Conținutul/descrierea meeting-ului
            $table->enum('priority', ['low', 'medium', 'high'])->default('medium');
            $table->text('notes')->nullable(); // Note despre meeting
            $table->boolean('send_email')->default(false); // Dacă să trimită email
            $table->enum('status', ['scheduled', 'completed', 'cancelled', 'rescheduled'])->default('scheduled');
            $table->timestamp('completed_at')->nullable(); // Când a fost completat
            $table->text('outcome')->nullable(); // Rezultatul meeting-ului
            $table->timestamps();
            
            // Index pentru căutări rapide
            $table->index(['lead_id', 'status']);
            $table->index(['scheduled_at', 'status']);
            $table->index('reminder_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('lead_meetings');
    }
};

// ALTERNATIV: Dacă preferi să adaugi coloane în tabela leads
// Adaugă această migrație pentru a modifica tabela leads:

/*
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('leads', function (Blueprint $table) {
            $table->timestamp('meeting_scheduled_at')->nullable()->after('lead_status_id');
            $table->string('meeting_title')->nullable()->after('meeting_scheduled_at');
            $table->text('meeting_notes')->nullable()->after('meeting_title');
            $table->enum('meeting_priority', ['low', 'medium', 'high'])->nullable()->after('meeting_notes');
            $table->boolean('meeting_send_email')->default(false)->after('meeting_priority');
        });
    }

    public function down(): void
    {
        Schema::table('leads', function (Blueprint $table) {
            $table->dropColumn([
                'meeting_scheduled_at', 
                'meeting_title', 
                'meeting_notes', 
                'meeting_priority', 
                'meeting_send_email'
            ]);
        });
    }
};
*/
