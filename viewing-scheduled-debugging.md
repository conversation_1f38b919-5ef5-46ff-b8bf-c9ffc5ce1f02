# 🔍 VIEWING SCHEDULED DEBUGGING

## 🎯 Problema identificată

Statusul "Viewing Scheduled" nu se salvează deși apare Toast-ul de success, dar restul statusurilor se salvează. Să investigăm de ce.

## 🔧 Debugging implementat

### **1. Adăugat logging specific pentru Viewing Scheduled:**

```typescript
// Handle Viewing Scheduled status
else if (selectedStatusObj.name === 'VIEWING_SCHEDULED' && config) {
  console.log('🔍 VIEWING_SCHEDULED Debug:');
  console.log('🔍 Config received:', config);
  console.log('🔍 Selected properties:', config.selectedProperties);
  console.log('🔍 Due date:', config.dueDate);
  console.log('🔍 Remarks:', config.remarks);
  
  // Adaugă listing_ids ca array de numbers
  if (config.selectedProperties && config.selectedProperties.length > 0) {
    payload.listing_ids = config.selectedProperties.map((propertyId: string) => parseInt(propertyId));
    console.log('🔍 Added listing_ids to payload:', payload.listing_ids);
  } else {
    console.log('🔍 No selected properties found!');
  }
  
  // ... rest of payload construction
}
```

### **2. Adăugat logging pentru payload final:**

```typescript
console.log('=== FINAL PAYLOAD DEBUG ===');
console.log('Status ID:', statusId);
console.log('Selected status object:', selectedStatusObj);
console.log('Final payload to send:', JSON.stringify(payload, null, 2));
console.log('=== END DEBUG INFO ===');
```

## 🔍 Testarea problemei

### **Test pentru Viewing Scheduled:**
1. Selectează "Viewing Scheduled"
2. Selectează cel puțin o proprietate
3. Completează due date și remarks
4. Apasă Save
5. **Verifică în console:**

**Ar trebui să vezi:**
```
🔍 VIEWING_SCHEDULED Debug:
🔍 Config received: {
  dueDate: "01/02/2025, 15:00",
  remarks: "Test viewing",
  selectedProperties: ["123", "456"]
}
🔍 Selected properties: ["123", "456"]
🔍 Due date: 01/02/2025, 15:00
🔍 Remarks: Test viewing
🔍 Added listing_ids to payload: [123, 456]

=== FINAL PAYLOAD DEBUG ===
Status ID: 14
Selected status object: {id: 14, name: "VIEWING_SCHEDULED", ...}
Final payload to send: {
  "statusId": "14",
  "listing_ids": [123, 456],
  "viewingScheduledData": {
    "reminder": {
      "title": "Viewing appointment",
      "content": "Viewing appointment reminder.",
      "priority": "low",
      "dueDate": "2025-02-01 15:00:00",
      "sendEmail": false,
      "sendReminderDate": "2025-02-01 15:00:00"
    },
    "listingSelection": {
      "123": "REF001",
      "456": "REF002"
    },
    "remarks": "Test viewing"
  },
  "remarks": "Test viewing"
}
```

## 🔍 Posibile cauze

### **1. Backend validation failure:**
```php
// Backend validează:
'listing_ids' => ['required', 'array', 'min:1'],
'reminder.dueDate' => ['date', 'after_or_equal:today'],

// Posibile probleme:
- listing_ids nu se trimite corect
- dueDate format incorect
- Alte validări care eșuează
```

### **2. Payload structure incorectă:**
```typescript
// Backend se așteaptă la:
{
  statusId: "14",
  listing_ids: [123, 456],
  reminder: {
    dueDate: "2025-02-01 15:00:00",
    // ...
  }
}

// Dar trimitem:
{
  statusId: "14",
  listing_ids: [123, 456],
  viewingScheduledData: {
    reminder: {
      dueDate: "2025-02-01 15:00:00",
      // ...
    }
  }
}
```

### **3. Properties selection issue:**
```typescript
// selectedProperties poate fi:
- undefined
- array gol []
- array cu string IDs ["123", "456"]
- array cu number IDs [123, 456]
```

### **4. Date format issue:**
```typescript
// formatDateForAPI returnează:
"2025-02-01 15:00:00"

// Backend se așteaptă la:
"2025-02-01" sau "2025-02-01 15:00:00"
```

## 🔧 Soluții posibile

### **Soluția 1: Fix payload structure**
```typescript
// În loc de viewingScheduledData, trimite direct:
payload.reminder = {
  title: config.reminderTitle || 'Viewing appointment',
  content: config.reminderContent || 'Viewing appointment reminder.',
  priority: config.priority?.toLowerCase() || 'low',
  dueDate: config.dueDate ? formatDateForAPI(config.dueDate) : undefined,
  sendEmail: config.sendEmail || false,
  sendReminderDate: config.dueDate ? formatDateForAPI(config.dueDate) : undefined,
};
```

### **Soluția 2: Verifică properties selection**
```typescript
// Asigură-te că selectedProperties există:
if (!config.selectedProperties || config.selectedProperties.length === 0) {
  console.error('No properties selected for viewing!');
  return;
}
```

### **Soluția 3: Verifică backend response**
```typescript
// În onError, verifică exact ce eroare returnează backend-ul:
console.error('Backend error for VIEWING_SCHEDULED:', error.response?.data);
```

## 📊 Comparație cu statusurile care funcționează

### **Meeting Scheduled (funcționează):**
```typescript
payload.viewingScheduledData = {
  reminder: { ... },
  listingSelection: {},
  remarks: config.remarks || '',
};
```

### **Follow Up (funcționează):**
```typescript
payload.viewingScheduledData = {
  reminder: { ... },
  listingSelection: {},
  remarks: config.remarks || '',
};
```

### **Viewing Scheduled (nu funcționează):**
```typescript
payload.listing_ids = [123, 456];  // ← Diferența principală
payload.viewingScheduledData = {
  reminder: { ... },
  listingSelection: { "123": "REF001", "456": "REF002" },
  remarks: config.remarks || '',
};
```

## 🎯 Următorii pași

### **1. Testează cu logging activat:**
1. Selectează Viewing Scheduled
2. Selectează proprietăți
3. Completează toate câmpurile
4. Apasă Save
5. **Verifică în console toate log-urile**

### **2. Analizează output-ul:**
- Se construiește config-ul corect?
- Se adaugă listing_ids la payload?
- Ce payload final se trimite?
- Ce răspuns/eroare vine de la backend?

### **3. Compară cu statusurile care funcționează:**
- Meeting Scheduled payload vs Viewing Scheduled payload
- Ce diferențe există?
- Care e structura corectă pentru backend?

## 🚀 Rezultatul așteptat

După debugging, ar trebui să identificăm:

1. **Cauza exactă** - payload structure, validation, sau properties selection
2. **Diferența față de statusurile care funcționează**
3. **Fix-ul corect** - modificare payload sau backend expectations

## 🔧 Instrucțiuni de testare

1. **Deschide Developer Console**
2. **Selectează Viewing Scheduled**
3. **Selectează cel puțin o proprietate**
4. **Completează due date și remarks**
5. **Apasă Save**
6. **Verifică în console:**
   - VIEWING_SCHEDULED Debug logs
   - FINAL PAYLOAD DEBUG
   - Backend response/error
7. **Raportează exact ce vezi**

**Testează și spune-mi ce log-uri vezi în console pentru Viewing Scheduled!** 🔍

## 📝 Checklist pentru debugging

- [ ] Config se construiește corect cu selectedProperties
- [ ] listing_ids se adaugă la payload ca array de numbers
- [ ] viewingScheduledData se construiește corect
- [ ] Payload final conține toate câmpurile necesare
- [ ] Backend returnează success sau error specific
- [ ] Optimistic update funcționează corect
- [ ] Cache se actualizează cu datele corecte

**Acum poți testa Viewing Scheduled și să vezi exact ce se întâmplă în console!** 📊
