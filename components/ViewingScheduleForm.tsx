import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Switch,
} from 'react-native';
import { Calendar } from 'lucide-react-native';
import DateTimePicker from '@react-native-community/datetimepicker';

interface ViewingConfig {
  search: string;
  rentSale: string;
  towerBuilding: string;
  bedrooms: string[];
  minArea: string;
  maxArea: string;
  priceMin: string;
  priceMax: string;
  selectedProperties: string[];
  reminderTitle: string;
  reminderContent: string;
  dueDate: string;
  priority: string;
  sendEmail: boolean;
  remarks: string;
}

interface ViewingScheduleFormProps {
  selectedProperties: string[];
  listings: any[];
  viewingConfig: ViewingConfig;
  updateViewingConfig: (field: keyof ViewingConfig, value: string | boolean) => void;
  handleDatePress: (configType: 'viewing') => void;
  onBack: () => void;
  onConfirm: () => void;
}

export default function ViewingScheduleForm({
  selectedProperties,
  listings,
  viewingConfig,
  updateViewingConfig,
  handleDatePress,
  onBack,
  onConfirm,
}: ViewingScheduleFormProps) {
  const [showDatePicker, setShowDatePicker] = useState(false);

  const handleLocalDatePress = () => {
    setShowDatePicker(true);
  };

  const handleDateChange = (_event: any, selectedDate?: Date) => {
    setShowDatePicker(false);

    if (selectedDate) {
      const formattedDate = selectedDate.toLocaleDateString('en-GB', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric',
      }) + ', ' + selectedDate.toLocaleTimeString('en-GB', {
        hour: '2-digit',
        minute: '2-digit',
        hour12: false,
      });

      updateViewingConfig('dueDate', formattedDate);
    }
  };
  const getContentWithRefNos = () => {
    // Dacă utilizatorul a editat deja conținutul, folosește valoarea editată
    if (viewingConfig.reminderContent && viewingConfig.reminderContent.trim() !== '') {
      return viewingConfig.reminderContent;
    }

    // Altfel, generează automat textul cu ref_no-urile
    if (selectedProperties.length > 0) {
      const refNos = selectedProperties.map(propertyId => {
        const property = listings.find(listing => listing.id.toString() === propertyId);
        return property?.ref_no;
      }).filter(Boolean).join(', ');

      return `This is just a reminder that the following listings needs to be seen: ${refNos}`;
    }
    return '';
  };

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      <Text style={styles.scheduleTitle}>
        Schedule viewing for {selectedProperties.length} properties
      </Text>

      <View style={styles.scheduleForm}>
        {/* Selected Properties */}
        <View style={styles.inputContainer}>
          <Text style={styles.inputLabel}>Selected Properties</Text>
          <View style={styles.selectedPropertiesContainer}>
            {selectedProperties.map(propertyId => {
              const property = listings.find(listing => listing.id.toString() === propertyId);
              return property ? (
                <View key={propertyId} style={styles.selectedPropertyItem}>
                  <Text style={styles.selectedPropertyText}>
                    {property.ref_no} - {property.property_type} {property.bedrooms_no ? `(${property.bedrooms_no} BR)` : ''}
                  </Text>
                </View>
              ) : null;
            })}
          </View>
        </View>

        {/* Reminder Section */}
        <Text style={styles.sectionTitle}>Reminder</Text>
        
        <View style={styles.inputContainer}>
          <Text style={styles.inputLabel}>Title</Text>
          <TextInput
            style={styles.textInput}
            placeholder="Visit appointment"
            value={viewingConfig.reminderTitle || ''}
            onChangeText={(text) => updateViewingConfig('reminderTitle', text)}
          />
        </View>

        <View style={styles.inputContainer}>
          <Text style={styles.inputLabel}>Content</Text>
          <TextInput
            style={[styles.textInput, styles.textArea]}
            placeholder="This is just a reminder that the following listings needs to be seen..."
            value={getContentWithRefNos()}
            onChangeText={(text) => updateViewingConfig('reminderContent', text)}
            multiline
            numberOfLines={4}
          />
        </View>

        <View style={styles.inputContainer}>
          <Text style={styles.inputLabel}>Due Date</Text>
          <View style={styles.dateInputContainer}>
            <TouchableOpacity
              style={styles.dateButton}
              onPress={handleLocalDatePress}
            >
              <Text style={styles.dateButtonText}>
                {viewingConfig.dueDate || 'Select date and time'}
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.calendarButton}
              onPress={handleLocalDatePress}
            >
              <Calendar size={20} color="#6B7280" />
            </TouchableOpacity>
          </View>
          {/* Inline Date Picker */}
          {showDatePicker && (
            <View style={styles.inlineDatePicker}>
              <DateTimePicker
                value={new Date()}
                mode="date"
                display="default"
                onChange={handleDateChange}
              />
            </View>
          )}
        </View>

        <View style={styles.inputContainer}>
          <Text style={styles.inputLabel}>Priority</Text>
          <View style={styles.priorityContainer}>
            {['Low', 'Medium', 'High'].map((priority) => (
              <TouchableOpacity
                key={priority}
                style={[
                  styles.priorityButton,
                  viewingConfig.priority === priority && styles.priorityButtonActive
                ]}
                onPress={() => updateViewingConfig('priority', priority)}
              >
                <Text
                  style={[
                    styles.priorityButtonText,
                    viewingConfig.priority === priority && styles.priorityButtonTextActive
                  ]}
                >
                  {priority}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        <View style={styles.switchContainer}>
          <Text style={styles.switchLabel}>Send Email</Text>
          <Switch
            value={viewingConfig.sendEmail || false}
            onValueChange={(value) => updateViewingConfig('sendEmail', value)}
            trackColor={{ false: '#E5E7EB', true: '#B89C4C' }}
            thumbColor={viewingConfig.sendEmail ? '#fff' : '#fff'}
          />
        </View>

        <View style={styles.inputContainer}>
          <Text style={styles.inputLabel}>Remarks</Text>
          <TextInput
            style={[styles.textInput, styles.textArea]}
            placeholder="Additional remarks..."
            value={viewingConfig.remarks || ''}
            onChangeText={(text) => updateViewingConfig('remarks', text)}
            multiline
            numberOfLines={3}
          />
        </View>
      </View>

      <View style={styles.modalButtonContainer}>
        <TouchableOpacity
          style={styles.backToPropertiesButton}
          onPress={onBack}
        >
          <Text style={styles.backToPropertiesButtonText}>Back to Properties</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={styles.finalScheduleButton}
          onPress={onConfirm}
        >
          <Text style={styles.finalScheduleButtonText}>Confirm Schedule</Text>
        </TouchableOpacity>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scheduleTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#111827',
    marginBottom: 16,
    textAlign: 'center',
  },
  scheduleForm: {
    paddingHorizontal: 16,
  },
  inputContainer: {
    marginBottom: 16,
  },
  inputLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: '#374151',
    marginBottom: 8,
  },
  textInput: {
    borderWidth: 1,
    borderColor: '#D1D5DB',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    fontSize: 16,
    color: '#111827',
  },
  textArea: {
    height: 80,
    textAlignVertical: 'top',
  },
  selectedPropertiesContainer: {
    backgroundColor: '#F9FAFB',
    borderRadius: 8,
    padding: 12,
    marginTop: 8,
  },
  selectedPropertyItem: {
    backgroundColor: '#fff',
    borderRadius: 6,
    padding: 8,
    marginBottom: 6,
    borderLeftWidth: 3,
    borderLeftColor: '#B89C4C',
  },
  selectedPropertyText: {
    fontSize: 12,
    color: '#374151',
    fontWeight: '500',
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#111827',
    marginBottom: 12,
    marginTop: 8,
  },
  dateInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    position: 'relative',
  },
  dateButton: {
    flex: 1,
    borderWidth: 1,
    borderColor: '#D1D5DB',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    marginTop: 8,
    marginRight: 8,
  },
  dateButtonText: {
    fontSize: 14,
    color: '#6B7280',
  },
  calendarButton: {
    position: 'absolute',
    right: 8,
    top: 18,
    padding: 4,
  },
  inlineDatePicker: {
    marginTop: 8,
  },
  priorityContainer: {
    flexDirection: 'row',
    marginTop: 8,
    gap: 8,
  },
  priorityButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#D1D5DB',
    backgroundColor: '#fff',
  },
  priorityButtonActive: {
    backgroundColor: '#B89C4C',
    borderColor: '#B89C4C',
  },
  priorityButtonText: {
    fontSize: 14,
    color: '#6B7280',
  },
  priorityButtonTextActive: {
    color: '#fff',
  },
  switchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginVertical: 16,
  },
  switchLabel: {
    fontSize: 14,
    color: '#374151',
    fontWeight: '500',
  },
  modalButtonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 12,
    paddingHorizontal: 16,
    paddingVertical: 16,
    marginBottom: 32, // ← Margin bottom mai mare
  },
  backToPropertiesButton: {
    flex: 1,
    backgroundColor: '#F3F4F6',
    borderRadius: 8,
    paddingVertical: 12,
    alignItems: 'center',
  },
  backToPropertiesButtonText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#6B7280',
  },
  finalScheduleButton: {
    flex: 1,
    backgroundColor: '#B89C4C',
    borderRadius: 8,
    paddingVertical: 12,
    alignItems: 'center',
  },
  finalScheduleButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#fff',
  },
});
