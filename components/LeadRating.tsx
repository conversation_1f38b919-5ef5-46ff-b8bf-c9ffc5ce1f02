import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { Star, Edit } from 'lucide-react-native';

interface LeadRatingProps {
    rating: string | null;
    showLabel?: boolean;
    size?: 'small' | 'medium' | 'large';
    showEditIcon?: boolean;
}

const getSizeConfig = (size: 'small' | 'medium' | 'large') => {
    switch (size) {
        case 'large':
            return {
                badge: { paddingHorizontal: 12, paddingVertical: 6 },
                icon: 18,
                text: 16,
                editIcon: 12,
            };
        case 'medium':
            return {
                badge: { paddingHorizontal: 10, paddingVertical: 5 },
                icon: 16,
                text: 14,
                editIcon: 10,
            };
        case 'small':
            return {
                badge: { paddingHorizontal: 8, paddingVertical: 4 },
                icon: 14,
                text: 12,
                editIcon: 8,
            };
    }
};

export default function LeadRating({ rating, showLabel = false, size = 'small', showEditIcon = false }: LeadRatingProps) {
    if (!rating) {
        const sizeConfig = getSizeConfig(size);
        return (
            <View style={[
                styles.badge,
                { backgroundColor: '#6b7280' },
                sizeConfig.badge,
            ]}>
                <Text style={[styles.text, { fontSize: sizeConfig.text }]}>
                    Non Rated
                </Text>
                {showEditIcon && (
                    <Edit size={sizeConfig.editIcon} color="#fff" style={styles.editIcon} />
                )}
            </View>
        );
    }

    const getRatingColor = (rating: string) => {
        switch (rating) {
            case 'A':
                return '#22c55e';
            case 'B':
                return '#3b82f6';
            case 'C':
                return '#f59e0b';
            case 'D':
                return '#fb923c';
            case 'E':
                return '#ef4444';
            case 'F':
                return '#991b1b';
            default:
                return '#6b7280';
        }
    };

    const sizeConfig = getSizeConfig(size);

    return (
        <View style={[
            styles.badge,
            { backgroundColor: getRatingColor(rating) },
            sizeConfig.badge,
        ]}>
            <Star size={sizeConfig.icon} color="#fff" />
            <Text style={[styles.text, { fontSize: sizeConfig.text }]}>
                {showLabel ? `Rating ${rating}` : rating}
            </Text>
            {showEditIcon && (
                <Edit size={sizeConfig.editIcon} color="#fff" style={styles.editIcon} />
            )}
        </View>
    );
}

const styles = StyleSheet.create({
    badge: {
        flexDirection: 'row',
        alignItems: 'center',
        borderRadius: 16,
        gap: 4,
    },
    text: {
        color: '#fff',
        fontWeight: '500',
    },
    editIcon: {
        opacity: 0.8,
    },
});