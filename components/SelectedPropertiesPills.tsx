import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
} from 'react-native';
import { router } from 'expo-router';

interface Property {
  id: number;
  ref_no?: string;
}

interface SelectedPropertiesPillsProps {
  selectedProperties: string[];
  listings: Property[];
  onRemoveProperty: (propertyId: string) => void;
  showNavigationOnPress?: boolean;
  title?: string;
}

export default function SelectedPropertiesPills({
  selectedProperties,
  listings,
  onRemoveProperty,
  showNavigationOnPress = true,
  title = "Selected",
}: SelectedPropertiesPillsProps) {
  if (selectedProperties.length === 0) {
    return null;
  }

  const handlePropertyPress = (property: Property) => {
    if (showNavigationOnPress) {
      router.push(`/inventory/${property.id}`);
    }
  };

  const getDisplayText = (propertyId: string) => {
    const property = listings.find(listing => listing.id.toString() === propertyId);
    return property?.ref_no || `Property ${propertyId}`;
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>{title} ({selectedProperties.length})</Text>
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
      >
        {selectedProperties.map(propertyId => {
          const property = listings.find(listing => listing.id.toString() === propertyId);
          const displayText = getDisplayText(propertyId);
          
          return (
            <TouchableOpacity
              key={propertyId}
              style={styles.pill}
              onPress={() => {
                if (property && showNavigationOnPress) {
                  handlePropertyPress(property);
                }
              }}
              activeOpacity={0.7}
            >
              <Text style={styles.pillText}>{displayText}</Text>
              <TouchableOpacity
                style={styles.removeButton}
                onPress={(e) => {
                  e.stopPropagation();
                  onRemoveProperty(propertyId);
                }}
              >
                <Text style={styles.removeText}>×</Text>
              </TouchableOpacity>
            </TouchableOpacity>
          );
        })}
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    backgroundColor: '#FFFFFF',
  },
  title: {
    fontSize: 14,
    fontWeight: '600',
    color: '#111827',
    marginBottom: 10,
  },
  scrollView: {
    flexDirection: 'row',
  },
  scrollContent: {
    paddingRight: 16,
    paddingLeft: 0,
  },
  pill: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#B89C4C',
    borderRadius: 16,
    paddingHorizontal: 12,
    paddingVertical: 6,
    marginRight: 8,
  },
  pillText: {
    fontSize: 12,
    fontWeight: '500',
    color: '#fff',
    marginRight: 6,
  },
  removeButton: {
    width: 16,
    height: 16,
    borderRadius: 8,
    backgroundColor: 'rgba(255, 255, 255, 0.25)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  removeText: {
    fontSize: 12,
    fontWeight: 'bold',
    color: '#fff',
    lineHeight: 16,
    textAlign: 'center',
  },
});
