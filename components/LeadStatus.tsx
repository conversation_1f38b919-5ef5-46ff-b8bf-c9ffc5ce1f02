import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { Edit } from 'lucide-react-native';

interface LeadStatusProps {
  name: string | null;
  backgroundColor?: string;
  size?: 'small' | 'medium' | 'large';
  showEditIcon?: boolean;
}

const getSizeConfig = (size: 'small' | 'medium' | 'large') => {
  switch (size) {
    case 'large':
      return {
        container: { paddingHorizontal: 12, paddingVertical: 6 },
        text: 14,
        editIcon: 12,
      };
    case 'medium':
      return {
        container: { paddingHorizontal: 10, paddingVertical: 5 },
        text: 12,
        editIcon: 10,
      };
    case 'small':
      return {
        container: { paddingHorizontal: 8, paddingVertical: 4 },
        text: 11,
        editIcon: 8,
      };
  }
};

export default function LeadStatus({ name, backgroundColor = '#3B82F6', size = 'small', showEditIcon = false }: LeadStatusProps) {
  const formatStatus = (status: string | null) => {
    if (!status) return 'New';
    return status
      .split('_')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join(' ');
  };

  const sizeConfig = getSizeConfig(size);

  return (
    <View style={[
      styles.container,
      { backgroundColor },
      sizeConfig.container,
    ]}>
      <Text style={[
        styles.text,
        { fontSize: sizeConfig.text }
      ]}>
        {formatStatus(name)}
      </Text>
      {showEditIcon && (
        <Edit size={sizeConfig.editIcon} color="#fff" style={styles.editIcon} />
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    borderRadius: 16,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 4,
  },
  text: {
    color: '#FFFFFF',
    fontWeight: '500',
  },
  editIcon: {
    opacity: 0.8,
  },
});