import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  Modal,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  Dimensions,
  TextInput,
} from 'react-native';
import DateTimePicker from '@react-native-community/datetimepicker';
import { X, Calendar } from 'lucide-react-native';

interface FollowUpConfig {
  title: string;
  content: string;
  dueDate: string;
  priority: string;
  sendEmail: boolean;
  remarks: string;
}

interface FollowUpConfigModalProps {
  visible: boolean;
  onClose: () => void;
  onSave: (config: FollowUpConfig) => void;
  initialConfig?: FollowUpConfig;
}

const { height: screenHeight } = Dimensions.get('window');

export default function FollowUpConfigModal({
  visible,
  onClose,
  onSave,
  initialConfig,
}: FollowUpConfigModalProps) {
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [followUpConfig, setFollowUpConfig] = useState<FollowUpConfig>({
    title: 'Follow up',
    content: 'Follow up reminder.',
    dueDate: '',
    priority: 'Low',
    sendEmail: false,
    remarks: '',
  });

  useEffect(() => {
    if (initialConfig) {
      setFollowUpConfig(initialConfig);
    } else {
      setFollowUpConfig({
        title: 'Follow up',
        content: 'Follow up reminder.',
        dueDate: '',
        priority: 'Low',
        sendEmail: false,
        remarks: '',
      });
    }
  }, [initialConfig, visible]);

  const updateFollowUpConfig = (field: keyof FollowUpConfig, value: string | boolean) => {
    setFollowUpConfig(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleDatePress = () => {
    console.log('Date picker pressed for follow up');
    setShowDatePicker(true);
  };

  const handleDateChange = (_event: any, selectedDate?: Date) => {
    setShowDatePicker(false);

    if (selectedDate) {
      const formattedDate = selectedDate.toLocaleDateString('en-GB', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric',
      }) + ', ' + selectedDate.toLocaleTimeString('en-GB', {
        hour: '2-digit',
        minute: '2-digit',
        hour12: false,
      });

      updateFollowUpConfig('dueDate', formattedDate);
    }
  };

  const handleSave = () => {
    onSave(followUpConfig);
    onClose();
  };

  const handleClose = () => {
    setFollowUpConfig({
      title: 'Follow up',
      content: 'Follow up reminder.',
      dueDate: '',
      priority: 'Low',
      sendEmail: false,
      remarks: '',
    });
    onClose();
  };

  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="slide"
      onRequestClose={handleClose}
    >
      <View style={styles.overlay}>
        <View style={styles.modalContainer}>
          {/* Header */}
          <View style={styles.header}>
            <Text style={styles.title}>Follow Up Configuration</Text>
            <TouchableOpacity onPress={handleClose} style={styles.closeButton}>
              <X size={20} color="#6B7280" />
            </TouchableOpacity>
          </View>

          <ScrollView style={styles.configContainer}>
            <Text style={styles.configTitle}>Lead configuration</Text>

            {/* Reminder Section */}
            <Text style={styles.sectionTitle}>Reminder</Text>

            {/* Title */}
            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>Title</Text>
              <TextInput
                style={styles.textInput}
                value={followUpConfig.title}
                onChangeText={(text) => updateFollowUpConfig('title', text)}
                placeholder="Follow up"
              />
            </View>

            {/* Content */}
            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>Content</Text>
              <TextInput
                style={[styles.textInput, styles.textArea]}
                value={followUpConfig.content}
                onChangeText={(text) => updateFollowUpConfig('content', text)}
                placeholder="Follow up reminder."
                multiline
                numberOfLines={3}
              />
            </View>

            {/* Due Date */}
            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>Due Date</Text>
              <View style={styles.dateInputContainer}>
                <TextInput
                  style={[styles.textInput, styles.dateInput]}
                  value={followUpConfig.dueDate}
                  onChangeText={(text) => updateFollowUpConfig('dueDate', text)}
                  placeholder="dd.mm.yyyy, --:--"
                />
                <TouchableOpacity
                  style={styles.calendarButton}
                  onPress={handleDatePress}
                >
                  <Calendar size={20} color="#6B7280" />
                </TouchableOpacity>
              </View>
              {/* Inline Date Picker */}
              {showDatePicker && (
                <View style={styles.inlineDatePicker}>
                  <DateTimePicker
                    value={new Date()}
                    mode="date"
                    display="default"
                    onChange={handleDateChange}
                  />
                </View>
              )}
              {!followUpConfig.dueDate && (
                <Text style={styles.errorText}>Due Date is required</Text>
              )}
            </View>

            {/* Priority */}
            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>Priority</Text>
              <TouchableOpacity style={styles.dropdownButton}>
                <Text style={styles.dropdownText}>{followUpConfig.priority}</Text>
                <Text style={styles.dropdownArrow}>▼</Text>
              </TouchableOpacity>
            </View>

            {/* Send Email */}
            <View style={styles.checkboxContainer}>
              <TouchableOpacity
                style={styles.checkbox}
                onPress={() => updateFollowUpConfig('sendEmail', !followUpConfig.sendEmail)}
              >
                {followUpConfig.sendEmail && <Text style={styles.checkmark}>✓</Text>}
              </TouchableOpacity>
              <Text style={styles.checkboxLabel}>Send Email</Text>
            </View>

            {/* Remarks Section */}
            <Text style={styles.sectionTitle}>Remarks</Text>

            {/* Remarks */}
            <View style={styles.inputContainer}>
              <TextInput
                style={[styles.textInput, styles.textArea]}
                value={followUpConfig.remarks}
                onChangeText={(text) => updateFollowUpConfig('remarks', text)}
                placeholder="Remarks"
                multiline
                numberOfLines={3}
              />
            </View>
          </ScrollView>

          {/* Save Button */}
          <View style={styles.buttonContainer}>
            <TouchableOpacity style={styles.saveButton} onPress={handleSave}>
              <Text style={styles.saveButtonText}>Save</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContainer: {
    backgroundColor: '#fff',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    maxHeight: screenHeight * 0.7,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: '#111827',
  },
  closeButton: {
    padding: 4,
  },
  configContainer: {
    padding: 16,
    maxHeight: screenHeight * 0.5,
  },
  configTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#111827',
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#111827',
    marginBottom: 12,
    marginTop: 8,
  },
  inputContainer: {
    marginBottom: 16,
  },
  inputLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: '#374151',
    marginBottom: 8,
  },
  textInput: {
    backgroundColor: '#fff',
    borderWidth: 1,
    borderColor: '#D1D5DB',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    fontSize: 16,
    color: '#111827',
  },
  textArea: {
    height: 80,
    textAlignVertical: 'top',
  },
  dateInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    position: 'relative',
  },
  dateInput: {
    flex: 1,
    paddingRight: 40,
  },
  calendarButton: {
    position: 'absolute',
    right: 8,
    top: 10,
    padding: 4,
  },
  inlineDatePicker: {
    marginTop: 8,
  },
  errorText: {
    fontSize: 12,
    color: '#EF4444',
    marginTop: 4,
  },
  dropdownButton: {
    backgroundColor: '#fff',
    borderWidth: 1,
    borderColor: '#D1D5DB',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  dropdownText: {
    fontSize: 16,
    color: '#111827',
  },
  dropdownArrow: {
    fontSize: 12,
    color: '#6B7280',
  },
  checkboxContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  checkbox: {
    width: 20,
    height: 20,
    borderRadius: 4,
    borderWidth: 2,
    borderColor: '#D1D5DB',
    backgroundColor: '#fff',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  checkmark: {
    color: '#B89C4C',
    fontSize: 12,
    fontWeight: 'bold',
  },
  checkboxLabel: {
    fontSize: 16,
    color: '#111827',
  },
  buttonContainer: {
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
  },
  saveButton: {
    backgroundColor: '#B89C4C',
    borderRadius: 8,
    paddingVertical: 12,
    alignItems: 'center',
  },
  saveButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#fff',
  },
});
