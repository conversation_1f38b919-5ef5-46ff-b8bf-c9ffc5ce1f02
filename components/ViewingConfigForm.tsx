import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  TextInput,
  Switch,
} from 'react-native';
import { Calendar } from 'lucide-react-native';
import DateTimePicker from '@react-native-community/datetimepicker';
import SelectedPropertiesPills from './SelectedPropertiesPills';

interface ViewingConfig {
  search: string;
  rentSale: string;
  towerBuilding: string;
  bedrooms: string[];
  minArea: string;
  maxArea: string;
  priceMin: string;
  priceMax: string;
  selectedProperties: string[];
  reminderTitle: string;
  reminderContent: string;
  dueDate: string;
  priority: string;
  sendEmail: boolean;
  remarks: string;
}

interface ViewingConfigFormProps {
  viewingConfig: ViewingConfig;
  updateViewingConfig: (field: keyof ViewingConfig, value: string | string[] | boolean) => void;
  selectedProperties: string[];
  listings: any[];
  onSearchProperties?: () => void;
}

export default function ViewingConfigForm({
  viewingConfig,
  updateViewingConfig,
  selectedProperties,
  listings,
  onSearchProperties,
}: ViewingConfigFormProps) {
  const [showDatePicker, setShowDatePicker] = useState(false);

  const handleLocalDatePress = () => {
    setShowDatePicker(true);
  };

  const handleDateChange = (_event: any, selectedDate?: Date) => {
    setShowDatePicker(false);

    if (selectedDate) {
      const formattedDate = selectedDate.toLocaleDateString('en-GB', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric',
      }) + ', ' + selectedDate.toLocaleTimeString('en-GB', {
        hour: '2-digit',
        minute: '2-digit',
        hour12: false,
      });

      updateViewingConfig('dueDate', formattedDate);
    }
  };

  const getContentWithRefNos = () => {
    if (selectedProperties.length > 0) {
      const refNos = selectedProperties.map(propertyId => {
        const property = listings.find(listing => listing.id.toString() === propertyId);
        return property?.ref_no;
      }).filter(Boolean).join(', ');

      return `This is just a reminder that the following listings needs to be seen: ${refNos}`;
    }
    return viewingConfig.reminderContent || '';
  };

  return (
    <ScrollView style={styles.configContainer} showsVerticalScrollIndicator={false}>

      {/* Search Properties Button - Only show if onSearchProperties is provided */}
      {onSearchProperties && (
        <View style={styles.section}>
          <TouchableOpacity
            style={styles.searchPropertiesButton}
            onPress={onSearchProperties}
          >
            <Text style={styles.searchPropertiesButtonText}>Search Properties</Text>
          </TouchableOpacity>
        </View>
      )}

      {/* Selected Properties Display */}
      <SelectedPropertiesPills
        selectedProperties={selectedProperties}
        listings={listings}
        onRemoveProperty={(propertyId) => {
          const newSelectedProperties = selectedProperties.filter(id => id !== propertyId);
          updateViewingConfig('selectedProperties', newSelectedProperties);
        }}
        showNavigationOnPress={true}
        title="Selected Properties"
      />

      {/* Reminder Section */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Reminder</Text>

        <View style={styles.inputContainer}>
        <Text style={styles.inputLabel}>Title</Text>
        <TextInput
          style={styles.textInput}
          placeholder="Visit appointment"
          value={viewingConfig.reminderTitle || ''}
          onChangeText={(text) => updateViewingConfig('reminderTitle', text)}
        />
      </View>

      <View style={styles.inputContainer}>
        <Text style={styles.inputLabel}>Content</Text>
        <TextInput
          style={[styles.textInput, styles.textArea]}
          placeholder="This is just a reminder that the following listings needs to be seen..."
          value={getContentWithRefNos()}
          onChangeText={(text) => updateViewingConfig('reminderContent', text)}
          multiline
          numberOfLines={4}
        />
      </View>

      <View style={styles.inputContainer}>
        <Text style={styles.inputLabel}>Due Date</Text>
        <View style={styles.dateInputContainer}>
          <TouchableOpacity
            style={styles.dateButton}
            onPress={handleLocalDatePress}
          >
            <Text style={styles.dateButtonText}>
              {viewingConfig.dueDate || 'Select date and time'}
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.calendarButton}
            onPress={handleLocalDatePress}
          >
            <Calendar size={20} color="#6B7280" />
          </TouchableOpacity>
        </View>
        {/* Inline Date Picker */}
        {showDatePicker && (
          <View style={styles.inlineDatePicker}>
            <DateTimePicker
              value={new Date()}
              mode="date"
              display="default"
              onChange={handleDateChange}
            />
          </View>
        )}
      </View>

      <View style={styles.inputContainer}>
        <Text style={styles.inputLabel}>Priority</Text>
        <View style={styles.priorityContainer}>
          {['Low', 'Medium', 'High'].map((priority) => (
            <TouchableOpacity
              key={priority}
              style={[
                styles.priorityButton,
                viewingConfig.priority === priority && styles.priorityButtonActive
              ]}
              onPress={() => updateViewingConfig('priority', priority)}
            >
              <Text
                style={[
                  styles.priorityButtonText,
                  viewingConfig.priority === priority && styles.priorityButtonTextActive
                ]}
              >
                {priority}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>

      <View style={styles.switchContainer}>
        <Text style={styles.switchLabel}>Send Email</Text>
        <Switch
          value={viewingConfig.sendEmail || false}
          onValueChange={(value) => updateViewingConfig('sendEmail', value)}
          trackColor={{ false: '#E5E7EB', true: '#B89C4C' }}
          thumbColor={viewingConfig.sendEmail ? '#fff' : '#fff'}
        />
      </View>

        <View style={styles.inputContainer}>
          <Text style={styles.inputLabel}>Remarks</Text>
          <TextInput
            style={[styles.textInput, styles.textArea]}
            placeholder="Additional remarks..."
            value={viewingConfig.remarks || ''}
            onChangeText={(text) => updateViewingConfig('remarks', text)}
            multiline
            numberOfLines={3}
          />
        </View>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  configContainer: {
    backgroundColor: '#FFFFFF',
    flex: 1,
  },
  section: {
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  configTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#111827',
    marginBottom: 16,
  },

  searchPropertiesButton: {
    backgroundColor: '#10B981',
    borderRadius: 8,
    paddingVertical: 12,
    alignItems: 'center',
  },
  searchPropertiesButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#fff',
  },
  inputContainer: {
    marginBottom: 16,
  },
  inputLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: '#374151',
    marginBottom: 8,
  },
  textInput: {
    borderWidth: 1,
    borderColor: '#D1D5DB',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    fontSize: 16,
    color: '#111827',
    backgroundColor: '#fff',
  },
  textArea: {
    height: 80,
    textAlignVertical: 'top',
  },
  selectedPropertiesContainer: {
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 12,
    borderWidth: 1,
    borderColor: '#D1D5DB',
  },
  selectedPropertyItem: {
    backgroundColor: '#F9FAFB',
    borderRadius: 6,
    padding: 8,
    marginBottom: 6,
    borderLeftWidth: 3,
    borderLeftColor: '#B89C4C',
  },
  selectedPropertyText: {
    fontSize: 12,
    color: '#374151',
    fontWeight: '500',
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#111827',
    marginBottom: 16,
    marginTop: 0,
  },
  dateInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    position: 'relative',
  },
  dateButton: {
    flex: 1,
    borderWidth: 1,
    borderColor: '#D1D5DB',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    backgroundColor: '#fff',
    marginRight: 8,
  },
  dateButtonText: {
    fontSize: 14,
    color: '#6B7280',
  },
  calendarButton: {
    position: 'absolute',
    right: 12,
    top: 5,
    padding: 4,
  },
  inlineDatePicker: {
    marginTop: 8,
  },
  priorityContainer: {
    flexDirection: 'row',
    gap: 8,
  },
  priorityButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#D1D5DB',
    backgroundColor: '#fff',
  },
  priorityButtonActive: {
    backgroundColor: '#B89C4C',
    borderColor: '#B89C4C',
  },
  priorityButtonText: {
    fontSize: 14,
    color: '#6B7280',
  },
  priorityButtonTextActive: {
    color: '#fff',
  },
  switchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginVertical: 16,
  },
  switchLabel: {
    fontSize: 14,
    color: '#374151',
    fontWeight: '500',
  },
  pillsScrollView: {
    flexDirection: 'row',
  },
  pillsScrollContent: {
    paddingRight: 16,
  },
  selectedPill: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#B89C4C',
    borderRadius: 16,
    paddingHorizontal: 12,
    paddingVertical: 6,
    marginRight: 8,
  },
  selectedPillText: {
    fontSize: 12,
    fontWeight: '500',
    color: '#fff',
    marginRight: 6,
  },
  removePillButton: {
    width: 16,
    height: 16,
    borderRadius: 8,
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  removePillText: {
    fontSize: 12,
    fontWeight: 'bold',
    color: '#fff',
    lineHeight: 14,
  },
});
