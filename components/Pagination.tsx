import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { ChevronLeft, ChevronRight } from 'lucide-react-native';

interface PaginationProps {
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
}

export default function Pagination({ currentPage, totalPages, onPageChange }: PaginationProps) {
  const isFirstPage = currentPage === 1;
  const isLastPage = currentPage >= totalPages || totalPages === 0;

  return (
    <View style={styles.container}>
      <TouchableOpacity
        style={[styles.button, isFirstPage && styles.buttonDisabled]}
        onPress={() => !isFirstPage && onPageChange(currentPage - 1)}
        disabled={isFirstPage}
      >
        <ChevronLeft size={20} color={isFirstPage ? '#9CA3AF' : '#4B5563'} />
      </TouchableOpacity>

      <Text style={styles.pageInfo}>
        {currentPage}
      </Text>

      <TouchableOpacity
        style={[styles.button, isLastPage && styles.buttonDisabled]}
        onPress={() => !isLastPage && onPageChange(currentPage + 1)}
        disabled={isLastPage}
      >
        <ChevronRight size={20} color={isLastPage ? '#9CA3AF' : '#4B5563'} />
      </TouchableOpacity>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    gap: 20,
  },
  button: {
    padding: 8,
    borderRadius: 8,
    backgroundColor: '#F3F4F6',
  },
  buttonDisabled: {
    backgroundColor: '#E5E7EB',
  },
  pageInfo: {
    fontSize: 14,
    color: '#4B5563',
    fontWeight: '500',
  },
});