import { StyleSheet } from 'react-native';
import Toast, { BaseToast, ErrorToast } from 'react-native-toast-message';

export const toastConfig = {
  success: (props: any) => (
    <BaseToast
      {...props}
      style={styles.successToast}
      contentContainerStyle={styles.contentContainer}
      text1Style={styles.text1}
      text2Style={styles.text2}
      text1NumberOfLines={3}
      text2NumberOfLines={5}
    />
  ),
  error: (props: any) => (
    <ErrorToast
      {...props}
      style={styles.errorToast}
      contentContainerStyle={styles.contentContainer}
      text1Style={styles.text1}
      text2Style={styles.text2}
      text1NumberOfLines={3}
      text2NumberOfLines={5}
    />
  ),
  warning: (props: any) => (
    <BaseToast
      {...props}
      style={styles.warningToast}
      contentContainerStyle={styles.contentContainer}
      text1Style={styles.text1}
      text2Style={styles.text2}
      text1NumberOfLines={3}
      text2NumberOfLines={5}
    />
  ),
};

const styles = StyleSheet.create({
  successToast: {
    borderLeftColor: '#10B981',
    borderLeftWidth: 5,
    backgroundColor: '#ECFDF5',
    borderRadius: 12,
    marginHorizontal: 16,
    marginTop: 80, // Space below header/title
    minHeight: 80,
    maxHeight: 200,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  errorToast: {
    borderLeftColor: '#DC2626',
    borderLeftWidth: 5,
    backgroundColor: '#FEF2F2',
    borderRadius: 12,
    marginHorizontal: 16,
    marginTop: 80, // Space below header/title
    minHeight: 80,
    maxHeight: 200,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  warningToast: {
    borderLeftColor: '#F59E0B',
    borderLeftWidth: 5,
    backgroundColor: '#FFFBEB',
    borderRadius: 12,
    marginHorizontal: 16,
    marginTop: 80, // Space below header/title
    minHeight: 80,
    maxHeight: 200,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  contentContainer: {
    paddingHorizontal: 20,
    paddingVertical: 16,
    minHeight: 60,
    maxWidth: '90%',
  },
  text1: {
    fontSize: 16,
    fontWeight: '700',
    color: '#111827',
    marginBottom: 6,
  },
  text2: {
    fontSize: 14,
    color: '#4B5563',
    lineHeight: 20,
  },
});