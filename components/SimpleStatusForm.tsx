import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
} from 'react-native';



interface SimpleStatusFormProps {
  statusName: string;
  onRemarksChange?: (remarks: string) => void;
}

export default function SimpleStatusForm({
  statusName,
  onRemarksChange,
}: SimpleStatusFormProps) {
  const [remarks, setRemarks] = useState('');

  const handleRemarksChange = (text: string) => {
    setRemarks(text);
    onRemarksChange?.(text);
  };

  const formatStatusName = (name: string) => {
    return name.replace(/_/g, ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase());
  };



  return (
    <View style={styles.configContainer}>
      <Text style={styles.configTitle}>Status Change</Text>
      
      <View style={styles.statusInfo}>
        <Text style={styles.statusLabel}>Changing status to:</Text>
        <Text style={styles.statusName}>{formatStatusName(statusName)}</Text>
      </View>

      <View style={styles.inputContainer}>
        <Text style={styles.inputLabel}>
          Remarks
        </Text>
        <TextInput
          style={[
            styles.textInput,
            styles.textArea
          ]}
          placeholder={`Add remarks for ${formatStatusName(statusName)} status...`}
          value={remarks}
          onChangeText={handleRemarksChange}
          multiline
          numberOfLines={4}
          placeholderTextColor="#9CA3AF"
        />
      </View>


    </View>
  );
}

const styles = StyleSheet.create({
  configContainer: {
    padding: 20,
  },
  configTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#1F2937',
    marginBottom: 20,
  },
  statusInfo: {
    backgroundColor: '#F3F4F6',
    padding: 16,
    borderRadius: 8,
    marginBottom: 20,
  },
  statusLabel: {
    fontSize: 14,
    color: '#6B7280',
    marginBottom: 4,
  },
  statusName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1F2937',
  },
  inputContainer: {
    marginBottom: 20,
  },
  inputLabel: {
    fontSize: 16,
    fontWeight: '500',
    color: '#374151',
    marginBottom: 8,
  },
  textInput: {
    backgroundColor: '#F9FAFB',
    borderWidth: 1,
    borderColor: '#D1D5DB',
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    color: '#1F2937',
  },
  textArea: {
    height: 100,
    textAlignVertical: 'top',
  },


});
