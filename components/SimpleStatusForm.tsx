import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
} from 'react-native';



interface SimpleStatusFormProps {
  statusName: string;
  statusId?: string;
  onRemarksChange?: (remarks: string) => void;
}

export default function SimpleStatusForm({
  statusName,
  statusId,
  onRemarksChange,
}: SimpleStatusFormProps) {
  const [remarks, setRemarks] = useState('');

  const handleRemarksChange = (text: string) => {
    setRemarks(text);
    onRemarksChange?.(text);
  };

  const formatStatusName = (name: string) => {
    return name.replace(/_/g, ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase());
  };

  // Statusurile care NU necesită remarks (conform backend: 14,15,23,26)
  const statusesThatDontRequireRemarks = ['14', '15', '23', '26'];
  const isRemarksRequired = statusId ? !statusesThatDontRequireRemarks.includes(statusId) : true;

  return (
    <View style={styles.configContainer}>
      <Text style={styles.configTitle}>Status Change</Text>
      
      <View style={styles.statusInfo}>
        <Text style={styles.statusLabel}>Changing status to:</Text>
        <Text style={styles.statusName}>{formatStatusName(statusName)}</Text>
      </View>

      <View style={styles.inputContainer}>
        <Text style={styles.inputLabel}>
          Remarks{isRemarksRequired && <Text style={styles.requiredAsterisk}> *</Text>}
        </Text>
        <TextInput
          style={[
            styles.textInput,
            styles.textArea,
            isRemarksRequired && remarks.trim() === '' && styles.textInputError
          ]}
          placeholder={`${isRemarksRequired ? 'Required: ' : ''}Add remarks for ${formatStatusName(statusName)} status...`}
          value={remarks}
          onChangeText={handleRemarksChange}
          multiline
          numberOfLines={4}
          placeholderTextColor="#9CA3AF"
        />
        {isRemarksRequired && remarks.trim() === '' && (
          <Text style={styles.errorText}>Remarks are required for this status</Text>
        )}
      </View>


    </View>
  );
}

const styles = StyleSheet.create({
  configContainer: {
    padding: 20,
  },
  configTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#1F2937',
    marginBottom: 20,
  },
  statusInfo: {
    backgroundColor: '#F3F4F6',
    padding: 16,
    borderRadius: 8,
    marginBottom: 20,
  },
  statusLabel: {
    fontSize: 14,
    color: '#6B7280',
    marginBottom: 4,
  },
  statusName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1F2937',
  },
  inputContainer: {
    marginBottom: 20,
  },
  inputLabel: {
    fontSize: 16,
    fontWeight: '500',
    color: '#374151',
    marginBottom: 8,
  },
  textInput: {
    backgroundColor: '#F9FAFB',
    borderWidth: 1,
    borderColor: '#D1D5DB',
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    color: '#1F2937',
  },
  textArea: {
    height: 100,
    textAlignVertical: 'top',
  },
  requiredAsterisk: {
    color: '#DC2626',
    fontWeight: '600',
  },
  textInputError: {
    borderColor: '#DC2626',
    borderWidth: 2,
  },
  errorText: {
    fontSize: 14,
    color: '#DC2626',
    marginTop: 4,
  },

});
