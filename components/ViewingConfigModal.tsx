import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  Modal,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  Dimensions,
  FlatList,
} from 'react-native';
import { X } from 'lucide-react-native';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import {
  fetchGeographies,
  fetchBedrooms,
  fetchListings,
  api
} from '@/lib/api';
import ListingCard from '@/components/ListingCard';
import Pagination from '@/components/Pagination';
// Constants for dropdown options
const RENT_SALE_OPTIONS = [
  { id: 'rent', label: 'Rent' },
  { id: 'sale', label: 'Sale' },
  { id: 'rent_sale', label: 'Rent & Sale' }
];

const MIN_AREA_OPTIONS = [
  { id: '', label: 'Min Area' },
  { id: '500', label: '500 sqft' },
  { id: '1000', label: '1000 sqft' },
  { id: '1500', label: '1500 sqft' },
  { id: '2000', label: '2000 sqft' },
  { id: '2500', label: '2500 sqft' },
  { id: '3000', label: '3000 sqft' }
];

const MAX_AREA_OPTIONS = [
  { id: '', label: 'Max Area' },
  { id: '1000', label: '1000 sqft' },
  { id: '1500', label: '1500 sqft' },
  { id: '2000', label: '2000 sqft' },
  { id: '2500', label: '2500 sqft' },
  { id: '3000', label: '3000 sqft' },
  { id: '5000', label: '5000 sqft' }
];

const PRICE_MIN_OPTIONS = [
  { id: '', label: 'Min Price' },
  { id: '1000', label: '1,000 QAR' },
  { id: '2000', label: '2,000 QAR' },
  { id: '3000', label: '3,000 QAR' },
  { id: '5000', label: '5,000 QAR' },
  { id: '10000', label: '10,000 QAR' }
];

const PRICE_MAX_OPTIONS = [
  { id: '', label: 'Max Price' },
  { id: '5000', label: '5,000 QAR' },
  { id: '10000', label: '10,000 QAR' },
  { id: '15000', label: '15,000 QAR' },
  { id: '20000', label: '20,000 QAR' },
  { id: '50000', label: '50,000 QAR' }
];

// Helper function to transform API data to dropdown format
const transformApiDataToDropdownOptions = (data: any[]) => {
  if (!Array.isArray(data)) return [];

  return data.map((item) => ({
    id: item.id?.toString() || '',
    label: item.name || item.title || item.label || 'Unknown',
    value: item,
  }));
};

interface ViewingConfig {
  search: string;
  rentSale: string;
  towerBuilding: string;
  bedrooms: string[];
  minArea: string;
  maxArea: string;
  priceMin: string;
  priceMax: string;
  selectedProperties: string[];
}

interface ViewingConfigModalProps {
  visible: boolean;
  onClose: () => void;
  onSave: (config: ViewingConfig) => void;
  initialConfig?: ViewingConfig;
}

const { height: screenHeight } = Dimensions.get('window');
const ITEMS_PER_PAGE = 10;

export default function ViewingConfigModal({
  visible,
  onClose,
  onSave,
  initialConfig,
}: ViewingConfigModalProps) {
  const queryClient = useQueryClient();
  const [currentPage, setCurrentPage] = useState(1);
  
  const [viewingConfig, setViewingConfig] = useState<ViewingConfig>({
    search: '',
    rentSale: '',
    towerBuilding: '',
    bedrooms: [],
    minArea: '',
    maxArea: '',
    priceMin: '',
    priceMax: '',
    selectedProperties: [],
  });

  // Fetch dropdown data
  const { data: geographies = [] } = useQuery({
    queryKey: ['geographies'],
    queryFn: fetchGeographies,
  });

  const { data: bedrooms = [] } = useQuery({
    queryKey: ['bedrooms'],
    queryFn: fetchBedrooms,
  });

  // Transform API data to dropdown format
  const locationOptions = transformApiDataToDropdownOptions(geographies);
  const bedroomOptions = transformApiDataToDropdownOptions(bedrooms);

  // Get selected location object for towers query
  const selectedLocation = geographies.find((geo: any) => geo.id?.toString() === viewingConfig.search);

  const { data: towers = [{ id: 'any', name: 'Any' }] } = useQuery({
    queryKey: ['towers', selectedLocation?.id],
    queryFn: async () => {
      if (!selectedLocation?.id) {
        return [{ id: 'any', name: 'Any' }];
      }
      try {
        const { data } = await api.get(`/geography/${selectedLocation.id}/towers`);
        return [{ id: 'any', name: 'Any' }, ...data];
      } catch (error) {
        console.error('Error fetching towers:', error);
        return [{ id: 'any', name: 'Any' }];
      }
    },
    enabled: true,
  });

  // Transform towers to dropdown format
  const towerBuildingOptions = transformApiDataToDropdownOptions(towers);
  const finalTowerOptions = towerBuildingOptions.length > 0 ? towerBuildingOptions : [
    { id: 'any', label: 'Any' },
    { id: 'test', label: 'Test Tower' }
  ];

  useEffect(() => {
    if (initialConfig) {
      setViewingConfig(initialConfig);
    } else {
      setViewingConfig({
        search: '',
        rentSale: '',
        towerBuilding: '',
        bedrooms: [],
        minArea: '',
        maxArea: '',
        priceMin: '',
        priceMax: '',
        selectedProperties: [],
      });
    }
    setCurrentPage(1);
  }, [initialConfig, visible]);

  const updateViewingConfig = (field: keyof ViewingConfig, value: string | string[] | (string | number)[]) => {
    setViewingConfig(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // Create filters object for listings API
  // Note: API only supports propertyType, adType, location, vt parameters
  const createListingFilters = (config: ViewingConfig) => {
    const filters: any = {};

    if (config.search) {
      filters.location = config.search;
    }

    if (config.rentSale) {
      const rentSaleMap: any = {
        'rent': 'rent',
        'sale': 'sale',
        'rent_sale': 'All'
      };
      filters.adType = rentSaleMap[config.rentSale] || 'All';
    }

    // Note: Other filters (tower, bedrooms, areas, prices) are not supported by the API
    // They will be used for UI filtering only

    return filters;
  };

  // Check if any API-supported filters are applied
  const hasFilters = viewingConfig?.search || viewingConfig?.rentSale;

  // Check if any UI-only filters are applied (for display purposes)
  const hasUIFilters = viewingConfig?.towerBuilding ||
                      (viewingConfig?.bedrooms && viewingConfig.bedrooms.length > 0) ||
                      viewingConfig?.priceMin || viewingConfig?.priceMax;

  // Create query key without selectedProperties to avoid re-fetching on selection changes
  const viewingConfigForQuery = {
    search: viewingConfig.search,
    rentSale: viewingConfig.rentSale,
    towerBuilding: viewingConfig.towerBuilding,
    bedrooms: viewingConfig.bedrooms,
    minArea: viewingConfig.minArea,
    maxArea: viewingConfig.maxArea,
    priceMin: viewingConfig.priceMin,
    priceMax: viewingConfig.priceMax,
  };

  // Fetch listings with pagination
  const { data: listingsData, isLoading: isLoadingListings } = useQuery({
    queryKey: ['modal-listings', viewingConfigForQuery, currentPage],
    queryFn: () => {
      if (!hasFilters) {
        const params = {
          propertyType: undefined as number | undefined,
          adType: 'All' as string,
          vt: 'master' as string
        };
        return fetchListings(currentPage, params);
      }
      const filters = createListingFilters(viewingConfig);
      const params = {
        ...filters,
        vt: 'master',
        adType: filters.adType || 'All'
      };
      return fetchListings(currentPage, params);
    },
    enabled: visible,
  });

  // Apply client-side filtering for unsupported API filters
  const applyClientSideFilters = (listings: any[]) => {
    if (!hasUIFilters) return listings;

    return listings.filter(listing => {
      // Tower/Building filter - using tower.id from Listing interface
      if (viewingConfig.towerBuilding && viewingConfig.towerBuilding !== 'any') {
        if (listing.tower?.id?.toString() !== viewingConfig.towerBuilding) {
          return false;
        }
      }

      // Bedrooms filter - using bedrooms_no from Listing interface
      if (viewingConfig.bedrooms && viewingConfig.bedrooms.length > 0) {
        if (!viewingConfig.bedrooms.includes(listing.bedrooms_no?.toString())) {
          return false;
        }
      }

      // Price filters - using price field (string) from Listing interface
      if (viewingConfig.priceMin) {
        const minPrice = parseInt(viewingConfig.priceMin);
        const listingPrice = parseInt(listing.price?.replace(/[^0-9]/g, '') || '0');
        if (listingPrice < minPrice) {
          return false;
        }
      }

      if (viewingConfig.priceMax) {
        const maxPrice = parseInt(viewingConfig.priceMax);
        const listingPrice = parseInt(listing.price?.replace(/[^0-9]/g, '') || '0');
        if (listingPrice > maxPrice) {
          return false;
        }
      }

      return true;
    });
  };

  const rawListings = listingsData?.data || [];
  const listings = applyClientSideFilters(rawListings);

  console.log('Final filtered listings count:', listings.length);
  const totalListings = listingsData?.total || 0;
  const totalPages = Math.ceil(totalListings / ITEMS_PER_PAGE);

  // Handle listing selection
  const toggleListingSelection = useCallback((listingId: string) => {
    setViewingConfig(prev => ({
      ...prev,
      selectedProperties: prev.selectedProperties.includes(listingId)
        ? prev.selectedProperties.filter(id => id !== listingId)
        : [...prev.selectedProperties, listingId]
    }));
  }, []);

  // Render listing item
  const renderListingItem = useCallback(({ item }: { item: any }) => (
    <TouchableOpacity
      style={styles.listingItem}
      onPress={() => toggleListingSelection(item.id.toString())}
      activeOpacity={0.7}
    >
      <View style={styles.listingCheckboxContainer}>
        <View
          style={[
            styles.listingCheckbox,
            viewingConfig.selectedProperties.includes(item.id.toString()) && styles.listingCheckboxSelected
          ]}
        >
          {viewingConfig.selectedProperties.includes(item.id.toString()) && (
            <Text style={styles.listingCheckmark}>✓</Text>
          )}
        </View>
      </View>
      <View style={styles.listingCardContainer}>
        <ListingCard
          listing={item}
          disableNavigation={true}
          compact={true}
        />
      </View>
    </TouchableOpacity>
  ), [viewingConfig.selectedProperties, toggleListingSelection]);

  const getItemLayout = useCallback((_: any, index: number) => ({
    length: 90,
    offset: 90 * index,
    index,
  }), []);

  const resetFilters = () => {
    setViewingConfig({
      search: '',
      rentSale: '',
      towerBuilding: '',
      bedrooms: [],
      minArea: '',
      maxArea: '',
      priceMin: '',
      priceMax: '',
      selectedProperties: [],
    });
    setCurrentPage(1);
    queryClient.removeQueries({ queryKey: ['modal-listings'] });
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handleSave = () => {
    onSave(viewingConfig);
    onClose();
  };

  const handleClose = () => {
    setViewingConfig({
      search: '',
      rentSale: '',
      towerBuilding: '',
      bedrooms: [],
      minArea: '',
      maxArea: '',
      priceMin: '',
      priceMax: '',
      selectedProperties: [],
    });
    setCurrentPage(1);
    queryClient.removeQueries({ queryKey: ['modal-listings'] });
    onClose();
  };

  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="slide"
      onRequestClose={handleClose}
    >
      <View style={styles.overlay}>
        <View style={styles.modalContainer}>
          {/* Header */}
          <View style={styles.header}>
            <Text style={styles.title}>Viewing Configuration</Text>
            <TouchableOpacity onPress={handleClose} style={styles.closeButton}>
              <X size={20} color="#6B7280" />
            </TouchableOpacity>
          </View>

          <ScrollView style={styles.configContainer}>
            <Text style={styles.configTitle}>Lead configuration</Text>
            
            {/* Location Row */}
            <View style={styles.fullWidthContainer}>
              <Text style={styles.inputLabel}>Location</Text>
              <ScrollView
                horizontal
                style={styles.pillScrollContainer}
                contentContainerStyle={styles.pillScrollContent}
                showsHorizontalScrollIndicator={false}
              >
                {locationOptions.map((option) => (
                  <TouchableOpacity
                    key={option.id}
                    style={[
                      styles.filterPill,
                      viewingConfig.search === option.id.toString() && styles.selectedFilterPill
                    ]}
                    onPress={() => {
                      const newValue = viewingConfig.search === option.id.toString() ? '' : option.id.toString();
                      updateViewingConfig('search', newValue);
                      updateViewingConfig('towerBuilding', '');
                    }}
                  >
                    <Text
                      style={[
                        styles.filterPillText,
                        viewingConfig.search === option.id.toString() && styles.selectedFilterPillText
                      ]}
                    >
                      {option.label}
                    </Text>
                  </TouchableOpacity>
                ))}
              </ScrollView>
            </View>

            {/* Tower/Building Row - Only show when location is selected */}
            {viewingConfig.search && (
              <View style={styles.fullWidthContainer}>
                <Text style={styles.inputLabel}>Tower/Building</Text>
                <ScrollView
                  horizontal
                  style={styles.pillScrollContainer}
                  contentContainerStyle={styles.pillScrollContent}
                  showsHorizontalScrollIndicator={false}
                >
                  {finalTowerOptions.map((option) => {
                    const isSelected = viewingConfig.towerBuilding === option.id.toString();
                    return (
                      <TouchableOpacity
                        key={option.id}
                        style={[
                          styles.filterPill,
                          isSelected && styles.selectedFilterPill
                        ]}
                        onPress={() => {
                          const newValue = isSelected ? '' : option.id.toString();
                          updateViewingConfig('towerBuilding', newValue);
                        }}
                      >
                        <Text
                          style={[
                            styles.filterPillText,
                            isSelected && styles.selectedFilterPillText
                          ]}
                        >
                          {option.label}
                        </Text>
                      </TouchableOpacity>
                    );
                  })}
                </ScrollView>
              </View>
            )}

            {/* Rent/Sale Row */}
            <View style={styles.fullWidthContainer}>
              <Text style={styles.inputLabel}>Rent/Sale</Text>
              <ScrollView
                horizontal
                style={styles.pillScrollContainer}
                contentContainerStyle={styles.pillScrollContent}
                showsHorizontalScrollIndicator={false}
              >
                {RENT_SALE_OPTIONS.map((option) => {
                  const isSelected = viewingConfig.rentSale === option.id.toString();
                  return (
                    <TouchableOpacity
                      key={option.id}
                      style={[
                        styles.filterPill,
                        isSelected && styles.selectedFilterPill
                      ]}
                      onPress={() => {
                        const newValue = isSelected ? '' : option.id.toString();
                        updateViewingConfig('rentSale', newValue);
                      }}
                    >
                      <Text
                        style={[
                          styles.filterPillText,
                          isSelected && styles.selectedFilterPillText
                        ]}
                      >
                        {option.label}
                      </Text>
                    </TouchableOpacity>
                  );
                })}
              </ScrollView>
            </View>

            {/* Min Area Row */}
            <View style={styles.fullWidthContainer}>
              <Text style={styles.inputLabel}>Min Area</Text>
              <ScrollView
                horizontal
                style={styles.pillScrollContainer}
                contentContainerStyle={styles.pillScrollContent}
                showsHorizontalScrollIndicator={false}
              >
                {MIN_AREA_OPTIONS.map((option) => {
                  const isSelected = viewingConfig.minArea === option.id.toString();
                  return (
                    <TouchableOpacity
                      key={option.id}
                      style={[
                        styles.filterPill,
                        isSelected && styles.selectedFilterPill
                      ]}
                      onPress={() => {
                        const newValue = isSelected ? '' : option.id.toString();
                        updateViewingConfig('minArea', newValue);
                      }}
                    >
                      <Text
                        style={[
                          styles.filterPillText,
                          isSelected && styles.selectedFilterPillText
                        ]}
                      >
                        {option.label}
                      </Text>
                    </TouchableOpacity>
                  );
                })}
              </ScrollView>
            </View>

            {/* Max Area Row */}
            <View style={styles.fullWidthContainer}>
              <Text style={styles.inputLabel}>Max Area</Text>
              <ScrollView
                horizontal
                style={styles.pillScrollContainer}
                contentContainerStyle={styles.pillScrollContent}
                showsHorizontalScrollIndicator={false}
              >
                {MAX_AREA_OPTIONS.map((option) => {
                  const isSelected = viewingConfig.maxArea === option.id.toString();
                  return (
                    <TouchableOpacity
                      key={option.id}
                      style={[
                        styles.filterPill,
                        isSelected && styles.selectedFilterPill
                      ]}
                      onPress={() => {
                        const newValue = isSelected ? '' : option.id.toString();
                        updateViewingConfig('maxArea', newValue);
                      }}
                    >
                      <Text
                        style={[
                          styles.filterPillText,
                          isSelected && styles.selectedFilterPillText
                        ]}
                      >
                        {option.label}
                      </Text>
                    </TouchableOpacity>
                  );
                })}
              </ScrollView>
            </View>

            {/* Price Min Row */}
            <View style={styles.fullWidthContainer}>
              <Text style={styles.inputLabel}>Price Min</Text>
              <ScrollView
                horizontal
                style={styles.pillScrollContainer}
                contentContainerStyle={styles.pillScrollContent}
                showsHorizontalScrollIndicator={false}
              >
                {PRICE_MIN_OPTIONS.map((option) => {
                  const isSelected = viewingConfig.priceMin === option.id.toString();
                  return (
                    <TouchableOpacity
                      key={option.id}
                      style={[
                        styles.filterPill,
                        isSelected && styles.selectedFilterPill
                      ]}
                      onPress={() => {
                        const newValue = isSelected ? '' : option.id.toString();
                        updateViewingConfig('priceMin', newValue);
                      }}
                    >
                      <Text
                        style={[
                          styles.filterPillText,
                          isSelected && styles.selectedFilterPillText
                        ]}
                      >
                        {option.label}
                      </Text>
                    </TouchableOpacity>
                  );
                })}
              </ScrollView>
            </View>

            {/* Price Max Row */}
            <View style={styles.fullWidthContainer}>
              <Text style={styles.inputLabel}>Price Max</Text>
              <ScrollView
                horizontal
                style={styles.pillScrollContainer}
                contentContainerStyle={styles.pillScrollContent}
                showsHorizontalScrollIndicator={false}
              >
                {PRICE_MAX_OPTIONS.map((option) => {
                  const isSelected = viewingConfig.priceMax === option.id.toString();
                  return (
                    <TouchableOpacity
                      key={option.id}
                      style={[
                        styles.filterPill,
                        isSelected && styles.selectedFilterPill
                      ]}
                      onPress={() => {
                        const newValue = isSelected ? '' : option.id.toString();
                        updateViewingConfig('priceMax', newValue);
                      }}
                    >
                      <Text
                        style={[
                          styles.filterPillText,
                          isSelected && styles.selectedFilterPillText
                        ]}
                      >
                        {option.label}
                      </Text>
                    </TouchableOpacity>
                  );
                })}
              </ScrollView>
            </View>

            {/* Bedrooms Row */}
            <View style={styles.fullWidthContainer}>
              <Text style={styles.inputLabel}>Bedrooms</Text>
              <ScrollView
                horizontal
                style={styles.pillScrollContainer}
                contentContainerStyle={styles.pillScrollContent}
                showsHorizontalScrollIndicator={false}
              >
                {bedroomOptions.map((option) => (
                  <TouchableOpacity
                    key={option.id}
                    style={[
                      styles.filterPill,
                      viewingConfig.bedrooms.includes(option.id.toString()) && styles.selectedFilterPill
                    ]}
                    onPress={() => {
                      const currentBedrooms = viewingConfig.bedrooms;
                      const bedroomId = option.id.toString();
                      const newBedrooms = currentBedrooms.includes(bedroomId)
                        ? currentBedrooms.filter(id => id !== bedroomId)
                        : [...currentBedrooms, bedroomId];
                      updateViewingConfig('bedrooms', newBedrooms);
                    }}
                  >
                    <Text
                      style={[
                        styles.filterPillText,
                        viewingConfig.bedrooms.includes(option.id.toString()) && styles.selectedFilterPillText
                      ]}
                    >
                      {option.label}
                    </Text>
                  </TouchableOpacity>
                ))}
              </ScrollView>
            </View>

            {/* Action Buttons */}
            <View style={styles.viewingButtonsContainer}>
              <TouchableOpacity
                style={styles.resetButton}
                onPress={resetFilters}
              >
                <Text style={styles.resetButtonText}>Reset filters</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[
                  styles.scheduleButton,
                  viewingConfig.selectedProperties.length === 0 && styles.scheduleButtonDisabled
                ]}
                disabled={viewingConfig.selectedProperties.length === 0}
                onPress={handleSave}
              >
                <Text style={[
                  styles.scheduleButtonText,
                  viewingConfig.selectedProperties.length === 0 && styles.scheduleButtonTextDisabled
                ]}>
                  Schedule view
                  {viewingConfig.selectedProperties.length > 0 && ` (${viewingConfig.selectedProperties.length})`}
                </Text>
              </TouchableOpacity>
            </View>

            {/* Listings Results */}
            {isLoadingListings ? (
              <View style={styles.listingsContainer}>
                <Text style={styles.listingsTitle}>Loading properties...</Text>
              </View>
            ) : listings.length > 0 ? (
              <View style={styles.listingsContainer}>
                <Text style={styles.listingsTitle}>
                  Properties ({listings.length} din {totalListings})
                </Text>
                <FlatList
                  data={listings}
                  keyExtractor={(item) => item.id.toString()}
                  renderItem={renderListingItem}
                  getItemLayout={getItemLayout}
                  style={styles.listingsList}
                  showsVerticalScrollIndicator={false}
                  nestedScrollEnabled={true}
                  removeClippedSubviews={true}
                  maxToRenderPerBatch={10}
                  windowSize={10}
                  ListFooterComponent={() =>
                    totalPages > 1 ? (
                      <View style={styles.paginationContainer}>
                        <Pagination
                          currentPage={currentPage}
                          totalPages={totalPages}
                          onPageChange={handlePageChange}
                        />
                      </View>
                    ) : null
                  }
                />
              </View>
            ) : (
              <View style={styles.listingsContainer}>
                <Text style={styles.listingsTitle}>No properties found</Text>
              </View>
            )}
          </ScrollView>
        </View>
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContainer: {
    backgroundColor: '#fff',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    maxHeight: screenHeight * 0.9,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: '#111827',
  },
  closeButton: {
    padding: 4,
  },
  configContainer: {
    padding: 16,
    maxHeight: screenHeight * 0.7,
  },
  configTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#111827',
    marginBottom: 16,
  },
  fullWidthContainer: {
    marginBottom: 16,
  },
  inputLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: '#374151',
    marginBottom: 8,
  },
  pillScrollContainer: {
    flexDirection: 'row',
  },
  pillScrollContent: {
    paddingRight: 16,
  },
  filterPill: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    borderWidth: 1,
    borderColor: '#B89C4C',
    backgroundColor: 'transparent',
    marginRight: 8,
  },
  selectedFilterPill: {
    backgroundColor: '#B89C4C',
  },
  filterPillText: {
    fontSize: 12,
    fontWeight: '500',
    color: '#B89C4C',
  },
  selectedFilterPillText: {
    color: '#fff',
  },
  viewingButtonsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginVertical: 16,
    gap: 12,
  },
  resetButton: {
    flex: 1,
    backgroundColor: '#F3F4F6',
    borderRadius: 8,
    paddingVertical: 12,
    alignItems: 'center',
  },
  resetButtonText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#6B7280',
  },
  scheduleButton: {
    flex: 2,
    backgroundColor: '#B89C4C',
    borderRadius: 8,
    paddingVertical: 12,
    alignItems: 'center',
  },
  scheduleButtonDisabled: {
    backgroundColor: '#E5E7EB',
  },
  scheduleButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#fff',
  },
  scheduleButtonTextDisabled: {
    color: '#9CA3AF',
  },
  listingsContainer: {
    marginTop: 16,
  },
  listingsTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#111827',
    marginBottom: 12,
  },
  listingsList: {
    maxHeight: 300,
  },
  listingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
    backgroundColor: '#F9FAFB',
    borderRadius: 8,
    padding: 8,
  },
  listingCheckboxContainer: {
    marginRight: 12,
  },
  listingCheckbox: {
    width: 20,
    height: 20,
    borderRadius: 4,
    borderWidth: 2,
    borderColor: '#D1D5DB',
    backgroundColor: '#fff',
    alignItems: 'center',
    justifyContent: 'center',
  },
  listingCheckboxSelected: {
    backgroundColor: '#B89C4C',
    borderColor: '#B89C4C',
  },
  listingCheckmark: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
  },
  listingCardContainer: {
    flex: 1,
  },
  paginationContainer: {
    marginTop: 16,
    alignItems: 'center',
  },
});
