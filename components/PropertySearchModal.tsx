import React, { useState, useCallback, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Modal,
  TouchableOpacity,
  FlatList,
  Dimensions,
} from 'react-native';
import { X } from 'lucide-react-native';
import { useQuery } from '@tanstack/react-query';
import { fetchListings } from '@/lib/api';
import SearchBar from '@/components/SearchBar';
import ListingCard from '@/components/ListingCard';
import Pagination from '@/components/Pagination';
import SelectedPropertiesPills from '@/components/SelectedPropertiesPills';

interface PropertySearchModalProps {
  visible: boolean;
  onClose: () => void;
  onSelectProperties: (selectedProperties: string[]) => void;
  initialSelectedProperties?: string[];
}

const { height: screenHeight } = Dimensions.get('window');
const ITEMS_PER_PAGE = 10;

export default function PropertySearchModal({
  visible,
  onClose,
  onSelectProperties,
  initialSelectedProperties = [],
}: PropertySearchModalProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [debouncedSearchQuery, setDebouncedSearchQuery] = useState('');
  const [selectedProperties, setSelectedProperties] = useState<string[]>(initialSelectedProperties);
  const [currentPage, setCurrentPage] = useState(1);

  // Reset selected properties when modal opens
  useEffect(() => {
    if (visible) {
      setSelectedProperties(initialSelectedProperties);
      setCurrentPage(1);
      setSearchQuery('');
    }
  }, [visible, initialSelectedProperties]);

  // Debounce search query to avoid too many API calls
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchQuery(searchQuery);
    }, 300); // 300ms delay

    return () => clearTimeout(timer);
  }, [searchQuery]);

  // Reset to page 1 when search query changes
  useEffect(() => {
    if (debouncedSearchQuery.trim()) {
      setCurrentPage(1);
    }
  }, [debouncedSearchQuery]);

  // Fetch properties - different strategy for search vs pagination
  const { data: listingsData, isLoading } = useQuery({
    queryKey: ['property-search', debouncedSearchQuery.trim() ? 'search' : currentPage, debouncedSearchQuery.trim()],
    queryFn: async () => {
      const params = {
        propertyType: undefined as number | undefined,
        adType: 'All' as string,
        vt: 'master' as string
      };

      // If searching, fetch fewer pages for better performance
      if (debouncedSearchQuery.trim()) {
        const searchResults = [];
        let totalFound = 0;

        // Fetch first 5 pages (50 properties) for faster search
        const promises = [];
        for (let page = 1; page <= 5; page++) {
          promises.push(fetchListings(page, params));
        }

        const results = await Promise.all(promises);

        // Combine all results
        for (const result of results) {
          if (result?.data) {
            searchResults.push(...result.data);
            totalFound = result.total || totalFound; // Use the total from any response
          }
        }

        return {
          data: searchResults,
          total: totalFound
        };
      } else {
        // Normal pagination when not searching
        return fetchListings(currentPage, params);
      }
    },
    enabled: visible,
  });

  const rawListings = listingsData?.data || [];
  const totalListings = listingsData?.total || 0;
  const totalPages = Math.ceil(totalListings / ITEMS_PER_PAGE);

  // Filter listings based on search query (title and ref_no)
  const allFilteredListings = rawListings.filter(listing => {
    if (!searchQuery.trim()) return true;

    const query = searchQuery.toLowerCase().trim();
    const title = listing.title?.toLowerCase() || '';
    const refNo = listing.ref_no?.toLowerCase() || '';

    return title.includes(query) || refNo.includes(query);
  });

  // Paginate search results if needed
  const SEARCH_RESULTS_PER_PAGE = 20;
  const totalFilteredListings = allFilteredListings.length;
  const totalFilteredPages = Math.ceil(totalFilteredListings / SEARCH_RESULTS_PER_PAGE);

  // Get current page of filtered results
  const startIndex = searchQuery.trim()
    ? (currentPage - 1) * SEARCH_RESULTS_PER_PAGE
    : 0;
  const endIndex = searchQuery.trim()
    ? startIndex + SEARCH_RESULTS_PER_PAGE
    : allFilteredListings.length;

  const filteredListings = searchQuery.trim()
    ? allFilteredListings.slice(startIndex, endIndex)
    : allFilteredListings;

  const handleToggleProperty = useCallback((propertyId: string) => {
    setSelectedProperties(prev =>
      prev.includes(propertyId)
        ? prev.filter(id => id !== propertyId)
        : [...prev, propertyId]
    );
  }, []);

  const handleConfirm = () => {
    onSelectProperties(selectedProperties);
    onClose();
  };

  const handleClose = () => {
    setSelectedProperties(initialSelectedProperties);
    setSearchQuery('');
    setCurrentPage(1);
    onClose();
  };

  const renderListingItem = useCallback(({ item }: { item: any }) => (
    <TouchableOpacity
      style={styles.listingItem}
      onPress={() => handleToggleProperty(item.id.toString())}
      activeOpacity={0.7}
    >
      <View style={styles.listingCheckboxContainer}>
        <View
          style={[
            styles.listingCheckbox,
            selectedProperties.includes(item.id.toString()) && styles.listingCheckboxSelected
          ]}
        >
          {selectedProperties.includes(item.id.toString()) && (
            <Text style={styles.listingCheckmark}>✓</Text>
          )}
        </View>
      </View>
      <View style={styles.listingCardContainer}>
        <ListingCard
          listing={item}
          disableNavigation={true}
          compact={true}
        />
      </View>
    </TouchableOpacity>
  ), [selectedProperties, handleToggleProperty]);

  const getItemLayout = useCallback((_: any, index: number) => ({
    length: 90,
    offset: 90 * index,
    index,
  }), []);

  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="slide"
      onRequestClose={handleClose}
    >
      <View style={styles.modalOverlay}>
        <View style={styles.modalContainer}>
          {/* Header */}
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>Search Properties</Text>
            <TouchableOpacity onPress={handleClose} style={styles.modalCloseButton}>
              <X size={24} color="#6B7280" />
            </TouchableOpacity>
          </View>

          {/* Search Bar */}
          <View style={styles.searchContainer}>
            <SearchBar
              value={searchQuery}
              onChangeText={setSearchQuery}
              placeholder="Search by title or ref no..."
              isLoading={isLoading}
            />
          </View>

          {/* Selected Properties Pills - Always visible at bottom */}
          <SelectedPropertiesPills
            selectedProperties={selectedProperties}
            listings={rawListings}
            onRemoveProperty={handleToggleProperty}
            showNavigationOnPress={true}
            title="Selected"
          />
          {/* Content */}
          <View style={styles.modalContent}>
            {isLoading ? (
              <View style={styles.loadingContainer}>
                <Text style={styles.loadingText}>
                  {searchQuery.trim() ? 'Searching all properties...' : 'Loading properties...'}
                </Text>
              </View>
            ) : filteredListings.length > 0 ? (
              <>
                <Text style={styles.propertiesCount}>
                  {searchQuery.trim()
                    ? `${totalFilteredListings} properties found for "${searchQuery}"${totalFilteredListings > SEARCH_RESULTS_PER_PAGE ? ` (showing ${filteredListings.length})` : ''}`
                    : `${filteredListings.length} properties`
                  }
                </Text>
                <FlatList
                  data={filteredListings}
                  keyExtractor={(item) => item.id.toString()}
                  renderItem={renderListingItem}
                  getItemLayout={getItemLayout}
                  style={styles.modalListingsList}
                  showsVerticalScrollIndicator={false}
                  removeClippedSubviews={true}
                  maxToRenderPerBatch={10}
                  windowSize={10}
                  ListFooterComponent={() => {
                    // Show pagination for normal browsing or search results with multiple pages
                    const showPagination = searchQuery.trim()
                      ? totalFilteredPages > 1
                      : totalPages > 1;
                    const pagesToShow = searchQuery.trim() ? totalFilteredPages : totalPages;

                    return showPagination ? (
                      <View style={styles.modalPaginationContainer}>
                        <Pagination
                          currentPage={currentPage}
                          totalPages={pagesToShow}
                          onPageChange={setCurrentPage}
                        />
                      </View>
                    ) : null;
                  }}
                />
              </>
            ) : (
              <View style={styles.noPropertiesContainer}>
                <Text style={styles.noPropertiesText}>
                  {searchQuery ? 'No properties found matching your search' : 'No properties found'}
                </Text>
              </View>
            )}
          </View>

          {/* Footer Buttons */}
          <View style={styles.modalButtonContainer}>
            <TouchableOpacity
              style={styles.cancelButton}
              onPress={handleClose}
            >
              <Text style={styles.cancelButtonText}>Cancel</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[
                styles.confirmButton,
                selectedProperties.length === 0 && styles.confirmButtonDisabled
              ]}
              disabled={selectedProperties.length === 0}
              onPress={handleConfirm}
            >
              <Text style={[
                styles.confirmButtonText,
                selectedProperties.length === 0 && styles.confirmButtonTextDisabled
              ]}>
                Select ({selectedProperties.length})
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContainer: {
    backgroundColor: '#fff',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    maxHeight: screenHeight * 0.9,
    minHeight: screenHeight * 0.7,
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#111827',
  },
  modalCloseButton: {
    padding: 4,
  },
  searchContainer: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  modalContent: {
    flex: 1,
    padding: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: '#6B7280',
  },
  propertiesCount: {
    fontSize: 14,
    color: '#6B7280',
    marginBottom: 12,
  },
  modalListingsList: {
    flex: 1,
  },
  modalPaginationContainer: {
    marginTop: 16,
    alignItems: 'center',
  },
  noPropertiesContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  noPropertiesText: {
    fontSize: 16,
    color: '#6B7280',
    textAlign: 'center',
  },
  listingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
    backgroundColor: '#F9FAFB',
    borderRadius: 8,
    padding: 8,
  },
  listingCheckboxContainer: {
    marginRight: 12,
  },
  listingCheckbox: {
    width: 20,
    height: 20,
    borderRadius: 4,
    borderWidth: 2,
    borderColor: '#D1D5DB',
    backgroundColor: '#fff',
    alignItems: 'center',
    justifyContent: 'center',
  },
  listingCheckboxSelected: {
    backgroundColor: '#B89C4C',
    borderColor: '#B89C4C',
  },
  listingCheckmark: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
  },
  listingCardContainer: {
    flex: 1,
  },
  modalButtonContainer: {
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
    flexDirection: 'row',
    gap: 12,
  },
  cancelButton: {
    flex: 1,
    backgroundColor: '#F3F4F6',
    borderRadius: 8,
    paddingVertical: 12,
    alignItems: 'center',
  },
  cancelButtonText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#6B7280',
  },
  confirmButton: {
    flex: 2,
    backgroundColor: '#10B981',
    borderRadius: 8,
    paddingVertical: 12,
    alignItems: 'center',
  },
  confirmButtonDisabled: {
    backgroundColor: '#E5E7EB',
  },
  confirmButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#fff',
  },
  confirmButtonTextDisabled: {
    color: '#9CA3AF',
  },
  selectedPillsContainer: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
    backgroundColor: '#F9FAFB',
  },
  selectedPillsTitle: {
    fontSize: 14,
    fontWeight: '500',
    color: '#374151',
    marginBottom: 8,
  },
  pillsScrollView: {
    flexDirection: 'row',
  },
  pillsScrollContent: {
    paddingRight: 16,
  },
  selectedPill: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#B89C4C',
    borderRadius: 16,
    paddingHorizontal: 12,
    paddingVertical: 6,
    marginRight: 8,
  },
  selectedPillText: {
    fontSize: 12,
    fontWeight: '500',
    color: '#fff',
    marginRight: 6,
  },
  removePillButton: {
    width: 16,
    height: 16,
    borderRadius: 8,
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  removePillText: {
    fontSize: 12,
    fontWeight: 'bold',
    color: '#fff',
    lineHeight: 14,
  },
});
