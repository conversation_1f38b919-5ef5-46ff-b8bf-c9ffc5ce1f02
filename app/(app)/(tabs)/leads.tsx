import React, { useCallback, useState, useMemo, useEffect, useRef } from 'react';
import { View, Text, StyleSheet, ScrollView, RefreshControl, TouchableOpacity, Platform } from 'react-native';
import { router, useFocusEffect } from 'expo-router';
import { useQuery, keepPreviousData, useQueryClient } from '@tanstack/react-query';
import { ClipboardList, Plus, Search } from 'lucide-react-native';
import LeadCard from '@/components/LeadCard';
import Pagination from '@/components/Pagination';
import LeadFilters from '@/components/LeadFilters';
import SearchBar from '@/components/SearchBar';
import { usePersistedFilters } from '@/hooks/usePersistedFilters';
import FloatingFilterButton from '@/components/FloatingFilterButton';
import FilterDialog from '@/components/FilterDialog';
import { fetchLeads } from '@/lib/api';
import * as GlobalTypes from '@/types/global';
import { useListingTypes } from '@/hooks/useConfiguration';

const ITEMS_PER_PAGE = 20;

const defaultFilters: GlobalTypes.LeadFilters = {
  status: 'all',
  sort: 'lastContact_desc',
  propertyType: null,
  listType: 'personal',
  page: 1,
};

const CreateButton = () => (
  <TouchableOpacity
    style={styles.createButton}
    onPress={() => router.push('/leads/create')}
  >
    <Plus size={24} color="#fff" />
  </TouchableOpacity>
);

const LeadFilterDialog = React.memo(({
  isVisible,
  onClose,
  filters,
  onFilterChange,
  propertyTypes,
  isLoading,
  leadsCount
}: GlobalTypes.LeadFilterDialogProps & { leadsCount?: number }) => {
  const handleReset = useCallback(() => {
    // Use the same defaultFilters as the Clear Filters button
    onFilterChange?.(defaultFilters);
  }, [onFilterChange]);

  const handleStatusChange = useCallback((status: string) => {
    onFilterChange?.({ status });
  }, [onFilterChange]);

  const handleSortChange = useCallback((sort: string) => {
    onFilterChange?.({ sort });
  }, [onFilterChange]);

  const handlePropertyTypeChange = useCallback((propertyType: number | null) => {
    onFilterChange?.({ propertyType });
  }, [onFilterChange]);

  const handleListTypeChange = useCallback((listType: string) => {
    onFilterChange?.({ listType });
  }, [onFilterChange]);

  const floatingButton = useMemo(() => (
    <TouchableOpacity
      style={styles.viewLeadsButton}
      onPress={onClose}
    >
      <Text style={styles.viewLeadsButtonText}>
        View {leadsCount || 0} leads
      </Text>
    </TouchableOpacity>
  ), [leadsCount, onClose]);

  return (
    <FilterDialog
      visible={isVisible}
      onClose={onClose}
      title="Filter Leads"
      floatingButton={floatingButton}
    >
      <LeadFilters
        selectedStatus={filters?.status ?? 'all'}
        selectedSort={filters?.sort ?? 'lastContact'}
        selectedPropertyType={filters?.propertyType ?? null}
        selectedListType={filters?.listType ?? 'personal'}
        propertyTypes={propertyTypes ?? []}
        onStatusChange={handleStatusChange}
        onSortChange={handleSortChange}
        onPropertyTypeChange={handlePropertyTypeChange}
        onListTypeChange={handleListTypeChange}
        onReset={handleReset}
        isLoading={isLoading}
        leadsCount={leadsCount}
        onViewLeads={onClose}
      />
    </FilterDialog>
  );
}, (prevProps, nextProps) => {
  // Custom comparison to prevent unnecessary re-renders
  return (
    prevProps.isVisible === nextProps.isVisible &&
    prevProps.leadsCount === nextProps.leadsCount &&
    prevProps.isLoading === nextProps.isLoading &&
    JSON.stringify(prevProps.filters) === JSON.stringify(nextProps.filters) &&
    prevProps.propertyTypes === nextProps.propertyTypes
  );
});

export default function LeadsScreen() {
  const [isFilterVisible, setIsFilterVisible] = useState<boolean>(false);
  const [searchQuery, setSearchQuery] = useState<string>('');
  const { filters, setFilters } = usePersistedFilters<GlobalTypes.LeadFilters>('lead-filters', defaultFilters);
  const { data: propertyTypes = [] } = useListingTypes();
  const queryClient = useQueryClient();



  const closeModal = useCallback(() => setIsFilterVisible(false), []);
  const openModal = useCallback(() => setIsFilterVisible(true), []);

  const {
    data,
    isLoading,
    isError,
    error,
    refetch,
    isFetching
  } = useQuery({
    queryKey: ['leads', {
      status: filters?.status,
      propertyType: filters?.propertyType,
      listType: filters?.listType,
      page: filters?.page,
      sort: filters?.sort,
    }],
    queryFn: () => {
      const queryParams = {
        page: filters?.page ?? 1,
        status: filters?.status ?? 'all',
        propertyType: filters?.propertyType ?? null,
        listType: filters?.listType ?? 'personal',
        sort: filters?.sort ?? 'lastContact_desc'
      };

      return fetchLeads(
        queryParams.page,
        queryParams.status,
        queryParams.propertyType,
        queryParams.listType,
        queryParams.sort
      );
    },
    staleTime: 1000 * 60 * 5, // Increase stale time
    gcTime: 1000 * 60 * 10, // Increase cache time
    placeholderData: keepPreviousData,
    refetchOnWindowFocus: false,
    refetchOnMount: false,
    refetchOnReconnect: false,
    retry: false, // Don't retry failed requests
    retryOnMount: false,
  });

  // Refresh data when screen comes into focus (e.g., after status change)
  useFocusEffect(
    useCallback(() => {
      console.log('📱 Leads screen focused, checking for data refresh...');

      // Check if we need to refresh leads data
      const shouldRefresh = queryClient.getQueryState(['leads'])?.isInvalidated;

      if (shouldRefresh) {
        console.log('🔄 Refreshing leads data due to invalidation...');
        refetch();
      }
    }, [queryClient, refetch])
  );

  const memoizedFilters = useMemo(() => filters ?? defaultFilters, [filters]);
  const memoizedLeadsCount = useMemo(() => (data as any)?.total || 0, [data]);
  const memoizedPropertyTypes = useMemo(() => propertyTypes, [propertyTypes]);

  // Stable ref to current filters to avoid recreating handleFilterChange
  const filtersRef = useRef(filters);
  filtersRef.current = filters;

  const handleFilterChange = useCallback(async (newFilters: Partial<GlobalTypes.LeadFilters>) => {
    if (!setFilters) return;

    const currentFilters = filtersRef.current;
    const updatedFilters = { ...currentFilters, ...newFilters };

    // Reset page to 1 when any filter other than page changes
    if (Object.keys(newFilters).some(key => key !== 'page')) {
      updatedFilters.page = 1;
    }

    try {
      await setFilters(updatedFilters);
    } catch (error) {
      console.error('Error in setFilters:', error);
    }
  }, [setFilters]); // Only setFilters as dependency - function never recreates!

  const { leads = [], totalPages = 1 } = useMemo(() => {
    if (!data) return { leads: [], totalPages: 1 };

    let filteredLeads = (data as any).leads || [];

    // Filter leads based on search query
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase().trim();
      filteredLeads = filteredLeads.filter((lead: any) => {
        const contactName = lead.contact?.name?.toLowerCase() || '';
        const contactEmail = lead.contact?.email_1?.toLowerCase() || '';
        const contactPhone = lead.contact?.mobile_1 || '';
        const refNo = lead.inquired_ref_no?.toLowerCase() || '';

        return contactName.includes(query) ||
               contactEmail.includes(query) ||
               contactPhone.includes(query) ||
               refNo.includes(query);
      });
    }

    return {
      leads: filteredLeads,
      totalPages: Math.ceil(((data as any).total || 0) / ITEMS_PER_PAGE)
    };
  }, [data, searchQuery]);

  if (isError) {
    return (
      <View style={[styles.container, styles.centerContent]}>
        <Text style={styles.errorText}>Failed to load leads</Text>
        <TouchableOpacity style={styles.retryButton} onPress={() => refetch()}>
          <Text style={styles.retryButtonText}>Try Again</Text>
        </TouchableOpacity>
      </View>
    );
  }

  if (!isLoading && leads.length === 0) {
    return (
      <View style={styles.container}>
        <View style={styles.searchContainer}>
          <SearchBar
            value={searchQuery}
            onChangeText={setSearchQuery}
            placeholder="Search leads by name, email, phone, or ref..."
            icon={<Search size={20} color="#6B7280" />}
          />
        </View>

        <View style={[styles.container, styles.emptyContainer]}>
          <View style={styles.emptyContent}>
          <View style={styles.emptyIconContainer}>
            <ClipboardList size={48} color="#9CA3AF" />
          </View>
          <Text style={styles.emptyTitle}>No leads found</Text>
          <Text style={styles.emptyDescription}>
            {filters?.status !== 'all' || filters?.propertyType !== null || filters?.listType !== 'personal'
              ? "Try adjusting your filters to see more leads"
              : "You don't have any leads at the moment"}
          </Text>
          {(filters?.status !== 'all' || filters?.propertyType !== null || filters?.listType !== 'personal') && (
            <TouchableOpacity
              style={styles.clearFiltersButton}
              onPress={() => handleFilterChange(defaultFilters)}
            >
              <Text style={styles.clearFiltersButtonText}>Clear Filters</Text>
            </TouchableOpacity>
          )}
        </View>
        <CreateButton />
        <FloatingFilterButton onPress={openModal} />

        <LeadFilterDialog
          isVisible={isFilterVisible}
          onClose={closeModal}
          filters={memoizedFilters}
          onFilterChange={handleFilterChange}
          propertyTypes={memoizedPropertyTypes}
          isLoading={isFetching}
          leadsCount={memoizedLeadsCount}
        />
        </View>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.searchContainer}>
        <SearchBar
          value={searchQuery}
          onChangeText={setSearchQuery}
          placeholder="Search leads by name, email, phone, or ref..."
          icon={<Search size={20} color="#6B7280" />}
        />
      </View>

      <ScrollView
        style={styles.leadsList}
        contentContainerStyle={styles.leadsListContent}
        refreshControl={
          <RefreshControl
            refreshing={isFetching}
            onRefresh={refetch}
            enabled={!isLoading}
          />
        }
      >
        {isLoading && <>{Array(5).fill(null).map((_, index) => (
          <LeadCard key={`skeleton-${index}`} isLoading={true} />
        ))}</>}
        {leads.map(lead => (
          <TouchableOpacity
            key={lead.id}
            onPress={() => router.push(`/leads/${lead.id}`)}
          >
            <LeadCard lead={lead} />
          </TouchableOpacity>
        ))}
      </ScrollView>

      {totalPages > 1 && (
        <View style={styles.paginationContainer}>
          <Pagination
            currentPage={filters?.page ?? 1}
            totalPages={totalPages}
            onPageChange={(page) => handleFilterChange({ page })}
          />
        </View>
      )}

      <LeadFilterDialog
        isVisible={isFilterVisible}
        onClose={closeModal}
        filters={memoizedFilters}
        onFilterChange={handleFilterChange}
        propertyTypes={memoizedPropertyTypes}
        isLoading={isFetching}
        leadsCount={memoizedLeadsCount}
      />

      <CreateButton />
      <FloatingFilterButton onPress={openModal} />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  searchContainer: {
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  centerContent: {
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  emptyContainer: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyContent: {
    alignItems: 'center',
    padding: 20,
    maxWidth: 400,
  },
  emptyIconContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: '#F3F4F6',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 24,
  },
  emptyTitle: {
    fontSize: 24,
    fontWeight: '600',
    color: '#1F2937',
    marginBottom: 12,
    textAlign: 'center',
  },
  emptyDescription: {
    fontSize: 16,
    color: '#6B7280',
    textAlign: 'center',
    marginBottom: 24,
    lineHeight: 24,
  },
  clearFiltersButton: {
    backgroundColor: '#B89C4C',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  clearFiltersButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  leadsList: {
    flex: 1,
  },
  leadsListContent: {
    padding: 20,
  },
  errorText: {
    color: '#EF4444',
    fontSize: 16,
    marginBottom: 16,
    textAlign: 'center',
  },
  retryButton: {
    backgroundColor: '#2563EB',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 8,
  },
  retryButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '500',
  },
  paginationContainer: {
    paddingVertical: 20,
    backgroundColor: '#fff',
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
  },
  createButton: {
    position: 'absolute',
    right: 20,
    bottom: 100,
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: '#B89C4C',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  viewLeadsButton: {
    backgroundColor: '#B89C4C',
    paddingVertical: 16,
    paddingHorizontal: 24,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
    width: '100%',
  },
  viewLeadsButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
});
