# ✅ PAYLOAD VIEWING SCHEDULED CU LISTING_IDS

## 🎯 Structura finală pentru Viewing Scheduled

Payload-ul pentru viewing scheduled include acum `listing_ids` ca array de numbers:

```typescript
// Pentru Viewing Scheduled
if (config.selectedProperties && config.selectedProperties.length > 0) {
  payload.listing_ids = config.selectedProperties.map((propertyId: string) => parseInt(propertyId));
  console.log('Added listing_ids to payload:', payload.listing_ids);
}
```

## 📋 Payload final pentru Viewing Scheduled

```json
{
  "status": 14,
  "agent": 123,
  "listing_ids": [11615, 11616, 11614],
  "viewingScheduledData": {
    "reminder": {
      "title": "Viewing appointment",
      "content": "This is just a reminder that the following listings needs to be seen.",
      "priority": "medium",
      "dueDate": "2025-07-30",
      "sendEmail": true,
      "sendReminderDate": "2025-07-30"
    },
    "listingSelection": {
      "11615": "AS-002007-3751",
      "11616": "AS-002008-3751",
      "11614": "OR-000653-3962"
    },
    "remarks": "Show premium properties"
  },
  "remarks": "Viewing scheduled"
}
```

## 🔧 Logica implementată

### **1. Extragerea listing_ids**
```typescript
// Convertește array-ul de string IDs în array de numbers
payload.listing_ids = config.selectedProperties.map((propertyId: string) => parseInt(propertyId));
```

### **2. Crearea listingSelection**
```typescript
// Creează obiect cu ID ca key și ref_no ca value
let listingSelection: { [key: string]: string } = {};
config.selectedProperties.forEach((propertyId: string) => {
  const property = listings.find(listing => listing.id.toString() === propertyId);
  if (property) {
    listingSelection[propertyId] = property.ref_no;
  }
});
```

### **3. Logging pentru debugging**
```typescript
console.log('Added listing_ids to payload:', payload.listing_ids);
```

## 📊 Exemple de date

### **Input din ViewingStatusForm:**
```typescript
config.selectedProperties = ["11615", "11616", "11614"]
```

### **Output în payload:**
```json
{
  "listing_ids": [11615, 11616, 11614],
  "viewingScheduledData": {
    "listingSelection": {
      "11615": "AS-002007-3751",
      "11616": "AS-002008-3751", 
      "11614": "OR-000653-3962"
    }
  }
}
```

## 🎯 Utilizarea în Laravel

### **Pentru listing_ids (array de numbers):**
```php
// Laravel poate folosi listing_ids direct pentru queries
$propertyIds = $validData['listing_ids']; // [11615, 11616, 11614]
$properties = Property::whereIn('id', $propertyIds)->get();
```

### **Pentru listingSelection (obiect cu ref_no):**
```php
// Laravel folosește listingSelection pentru proposals și email-uri
foreach ($validData['viewingScheduledData']['listingSelection'] as $listingId => $listingRefNo) {
    // Creează proposals
    $newProposal = new LeadProposal();
    $newProposal->lead_id = $lead->id;
    $newProposal->property_id = $listingId;
    $newProposal->save();
}

// Pentru email-uri
$properties = Property::with('contact', 'author')
    ->whereIn('ref_no', array_values($validData['viewingScheduledData']['listingSelection']))
    ->get();
```

## ✅ Beneficii

### **1. Flexibilitate pentru Laravel:**
- **`listing_ids`** - array simplu de IDs pentru queries rapide
- **`listingSelection`** - obiect cu ref_no pentru compatibilitate cu codul existent

### **2. Debugging ușor:**
- **Logging clar** - vezi exact ce IDs se trimit
- **Structură transparentă** - vezi atât IDs cât și ref_no-urile

### **3. Compatibilitate:**
- **Cu codul existent** - `listingSelection` funcționează ca înainte
- **Cu noi funcționalități** - `listing_ids` permite queries mai eficiente

## 🔍 Cum să testezi

### **Pasul 1: Selectează proprietăți**
1. Alege status "Viewing Scheduled"
2. Selectează 2-3 proprietăți
3. Verifică că sunt afișate în UI

### **Pasul 2: Verifică logging-ul**
Când apeși "Save Status", verifică în console:
```
Added listing_ids to payload: [11615, 11616, 11614]
```

### **Pasul 3: Verifică payload-ul complet**
În debug info, verifică că payload-ul conține:
- ✅ `listing_ids` ca array de numbers
- ✅ `viewingScheduledData.listingSelection` ca obiect cu ref_no
- ✅ `viewingScheduledData.reminder` cu toate detaliile

## 📋 Interfața TypeScript actualizată

```typescript
export interface StatusChangeRequest {
  status: number;
  agent?: number;
  remarks?: string;
  listing_ids?: number[];  // ← Array de property IDs
  viewingScheduledData?: {
    reminder: {
      title?: string;
      content?: string;
      priority?: 'low' | 'medium' | 'high';
      dueDate?: string;
      sendEmail?: boolean;
      sendReminderDate?: string;
    };
    listingSelection?: { [key: string]: string };  // ← Obiect cu ref_no
    remarks?: string;
  };
}
```

## 🚀 Rezultat final

**Payload-ul pentru Viewing Scheduled include acum:**

- ✅ **`listing_ids`** - array de numbers pentru queries eficiente
- ✅ **`listingSelection`** - obiect cu ref_no pentru compatibilitate
- ✅ **`viewingScheduledData`** - structura completă pentru reminder
- ✅ **Logging** - pentru debugging ușor

**Laravel poate folosi ambele formate după necesități!** 🎉
