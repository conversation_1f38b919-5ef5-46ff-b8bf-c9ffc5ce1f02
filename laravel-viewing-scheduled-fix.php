<?php

// Înlocuiește funcția statusChange cu această versiune completă:

public function statusChange($leadId)
{
    $lead = Lead::find($leadId);
    
    if (is_null($lead)) {
        abort(404, 'Lead not found');
    }
    
    $agent = $lead->latestAssignment->user;
    if ($agent->id !== auth()->user()->id && !auth()->user()->hasRole(RolesDef::OFFICE_MANAGER)) {
        abort(403, 'You are not authorized to change this lead status');
    }
    
    $newLeadStatusId = request()->get('statusId');
    $statusesMap = $this->leadStatusService->getLeadStatusesMap();
    
    if ($newLeadStatusId == $statusesMap[LeadStatus::STATUS_MEETING_SCHEDULED]) {
        $createReminder = true;
        $arrayToValidate = [
            'reminder.title' => ['nullable', 'string', 'max:255'],
            'reminder.content' => ['nullable', 'string'],
            'reminder.due_date' => ['nullable', 'date', 'after_or_equal:today'],
            'reminder.priority' => ['nullable', 'in:low,medium,high'],
            'reminder.remarks' => ['nullable', 'string'],
            'reminder.send_email' => ['nullable', 'boolean'],
            'reminder.send_reminder_date' => ['nullable', 'date', 'before_or_equal:reminder.due_date'],
        ];
    } elseif ($newLeadStatusId == $statusesMap[LeadStatus::STATUS_VIEWING_SCHEDULED]) {
        $createReminder = true;
        $arrayToValidate = [
            'listing_ids' => ['required', 'array', 'min:1'],
            'listing_ids.*' => ['integer', 'exists:listings,id'], // Validează că fiecare ID există în tabela listings
            'reminder.title' => ['nullable', 'string', 'max:255'],
            'reminder.content' => ['nullable', 'string'],
            'reminder.due_date' => ['nullable', 'date', 'after_or_equal:today'],
            'reminder.priority' => ['nullable', 'in:low,medium,high'],
            'reminder.remarks' => ['nullable', 'string'],
            'reminder.send_email' => ['nullable', 'boolean'],
            'reminder.send_reminder_date' => ['nullable', 'date', 'before_or_equal:reminder.due_date'],
        ];
    } elseif ($newLeadStatusId == $statusesMap[LeadStatus::STATUS_FOLLOW_UP]) {
        $createReminder = true;
        $arrayToValidate = [
            'reminder.title' => ['nullable', 'string', 'max:255'],
            'reminder.content' => ['nullable', 'string'],
            'reminder.due_date' => ['nullable', 'date', 'after_or_equal:today'],
            'reminder.priority' => ['nullable', 'in:low,medium,high'],
            'reminder.remarks' => ['nullable', 'string'],
            'reminder.send_email' => ['nullable', 'boolean'],
            'reminder.send_reminder_date' => ['nullable', 'date', 'before_or_equal:reminder.due_date'],
        ];
    } else {
        $createReminder = false;
        $arrayToValidate = ['remark' => 'required'];
    }
    
    $validData = request()->validate($arrayToValidate);
    
    // Creează reminder-ul dacă e necesar
    if ($createReminder) {
        $reminderData = $validData['reminder'] ?? [];
        $reminderData['object_type'] = 'leads';
        $reminderData['object_id'] = $lead->id;
        $reminder = Reminder::create($reminderData);
        Log::info('Reminder created for lead #' . $lead->id . ' with ID: ' . $reminder->id);
    }
    
    // Pentru VIEWING_SCHEDULED, salvează și listing_ids
    if ($newLeadStatusId == $statusesMap[LeadStatus::STATUS_VIEWING_SCHEDULED] && isset($validData['listing_ids'])) {
        // Opțiunea 1: Salvează în tabela pivot lead_listings (dacă există)
        // $lead->listings()->sync($validData['listing_ids']);
        
        // Opțiunea 2: Salvează în JSON în tabela leads (dacă ai coloana viewing_properties)
        // $lead->viewing_properties = json_encode($validData['listing_ids']);
        
        // Opțiunea 3: Salvează în tabela separată lead_viewing_schedules
        // Șterge programările anterioare pentru acest lead
        DB::table('lead_viewing_schedules')->where('lead_id', $lead->id)->delete();
        
        // Adaugă noile programări
        foreach ($validData['listing_ids'] as $listingId) {
            DB::table('lead_viewing_schedules')->insert([
                'lead_id' => $lead->id,
                'listing_id' => $listingId,
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        }
        
        Log::info('Viewing scheduled for lead #' . $lead->id . ' with ' . count($validData['listing_ids']) . ' properties');
    }

    // Actualizează statusul lead-ului
    $newStatus = $this->leadStatusService->getLeadStatuses()->firstWhere('id', $newLeadStatusId);
    if (is_null($newStatus)) {
        abort(400, 'Invalid status ID');
    }
    
    $newStatusLabel = $newStatus->name;
    $previousStatus = $lead->leadStatus;
    $oldStatusLabel = 'NO STATUS';
    if (!is_null($previousStatus)) {
        $oldStatusLabel = $previousStatus->name;
    }

    $this->leadsService->trackLeadStatusChange($lead, $newStatus);

    $lead->lead_status_id = $newStatus->id;
    $lead->save();

    // Creează mesajul pentru operation history
    $remarksBody = "Lead status updated from [" . $oldStatusLabel . "] to [" . $newStatusLabel . "]";
    
    // Adaugă informații specifice pentru fiecare tip de status
    if ($newLeadStatusId == $statusesMap[LeadStatus::STATUS_VIEWING_SCHEDULED] && isset($validData['listing_ids'])) {
        $remarksBody .= " with " . count($validData['listing_ids']) . " properties scheduled for viewing";
    } elseif ($newLeadStatusId == $statusesMap[LeadStatus::STATUS_MEETING_SCHEDULED]) {
        $remarksBody .= " with meeting scheduled";
    } elseif ($newLeadStatusId == $statusesMap[LeadStatus::STATUS_FOLLOW_UP]) {
        $remarksBody .= " with follow-up scheduled";
    }
    
    Log::info($remarksBody . ' by ' . auth()->user()->name);
    $this->operationHistoryService->addOperationHistory($lead, $remarksBody, auth()->user());
    
    // Adaugă remark-ul utilizatorului dacă există
    if (array_key_exists('remark', $validData)) {
        $this->operationHistoryService->addOperationHistory($lead, $validData['remark'], auth()->user());
    }
    
    $lead->load('leadStatus');
    
    // Returnează răspuns de succes
    return response()->json([
        'success' => true,
        'message' => 'Lead status updated successfully',
        'lead' => $lead,
        'status' => $newStatus,
        'reminder_created' => $createReminder,
        'properties_scheduled' => isset($validData['listing_ids']) ? count($validData['listing_ids']) : 0
    ]);
}
