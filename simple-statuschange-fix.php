<?php

// SOLUȚIA SIMPLĂ: În<PERSON>ui<PERSON><PERSON><PERSON> doar HTTP call-ul cu apel direct

public function statusChange($leadId)
{
    $lead = Lead::find($leadId);
    $agent = $lead->latestAssignment->user;
    
    if (is_null($lead) || ($agent->id !== auth()->user()->id && !auth()->user()->hasRole(RolesDef::OFFICE_MANAGER))) {
        abort(403, 'You are not authorized to change this lead status');
    }
    
    // În loc de HTTP::post, setează datele în request și apelează direct updateLeadStatus
    request()->merge([
        'agent' => $agent->id,
        'status' => request()->get('status'),
        'remarks' => request()->get('remarks'),
        'viewingScheduledData' => request()->get('viewingScheduledData'),
    ]);
    
    // Apelează direct updateLeadStatus
    return $this->updateLeadStatus($leadId);
}

// SAU, dacă updateLeadStatus este într-un alt controller:

public function statusChange($leadId)
{
    $lead = Lead::find($leadId);
    $agent = $lead->latestAssignment->user;
    
    if (is_null($lead) || ($agent->id !== auth()->user()->id && !auth()->user()->hasRole(RolesDef::OFFICE_MANAGER))) {
        abort(403, 'You are not authorized to change this lead status');
    }
    
    // Creează o instanță a controller-ului care conține updateLeadStatus
    $updateController = new \App\Http\Controllers\YourUpdateController(); // înlocuiește cu numele corect
    
    // Setează datele în request
    request()->merge([
        'agent' => $agent->id,
        'status' => request()->get('status'),
        'remarks' => request()->get('remarks'),
        'viewingScheduledData' => request()->get('viewingScheduledData'),
    ]);
    
    // Apelează updateLeadStatus
    return $updateController->updateLeadStatus($leadId);
}

// SAU, cea mai simplă soluție - elimină complet HTTP call-ul și mută logica:

public function statusChange($leadId)
{
    $lead = Lead::find($leadId);
    $agent = $lead->latestAssignment->user;
    
    if (is_null($lead) || ($agent->id !== auth()->user()->id && !auth()->user()->hasRole(RolesDef::OFFICE_MANAGER))) {
        abort(403, 'You are not authorized to change this lead status');
    }
    
    // Pregătește datele exact cum le așteaptă updateLeadStatus
    $requestData = [
        'agent' => $agent->id,
        'status' => request()->get('status'),
        'remarks' => request()->get('remarks'),
        'viewingScheduledData' => request()->get('viewingScheduledData'),
    ];
    
    // Adaugă datele în request
    foreach ($requestData as $key => $value) {
        request()->merge([$key => $value]);
    }
    
    // Log pentru debugging
    Log::info('StatusChange - Request data prepared:', $requestData);
    Log::info('StatusChange - Calling updateLeadStatus for lead: ' . $leadId);
    
    try {
        // Apelează direct updateLeadStatus
        $result = $this->updateLeadStatus($leadId);
        
        Log::info('StatusChange - updateLeadStatus completed successfully');
        return $result;
        
    } catch (\Exception $e) {
        Log::error('StatusChange - Error in updateLeadStatus: ' . $e->getMessage());
        return response()->json([
            'success' => false,
            'message' => 'Failed to update lead status: ' . $e->getMessage()
        ], 500);
    }
}
