// Test file to verify the status update implementation
// This file demonstrates how to use the new updateLeadStatus function

import { updateLeadStatus, StatusChangeRequest } from './lib/api';

// Example 1: Simple status change with remarks only
const simpleStatusChange: StatusChangeRequest = {
  statusId: 1,
  remark: 'Status changed to new lead'
};

// Example 2: Meeting scheduled status with reminder
const meetingScheduledStatus: StatusChangeRequest = {
  statusId: 2, // Assuming this is the ID for MEETING_SCHEDULED
  reminder: {
    title: 'Meeting appointment',
    content: 'Meeting appointment reminder.',
    due_date: '2024-01-15',
    priority: 'medium',
    remarks: 'Important client meeting',
    send_email: true,
    send_reminder_date: '2024-01-15'
  }
};

// Example 3: Viewing scheduled status with properties and reminder
const viewingScheduledStatus: StatusChangeRequest = {
  statusId: 3, // Assuming this is the ID for VIEWING_SCHEDULED
  listing_ids: [101, 102, 103],
  reminder: {
    title: 'Viewing appointment',
    content: 'Property viewing reminder.',
    due_date: '2024-01-20',
    priority: 'high',
    remarks: 'Show premium properties',
    send_email: true,
    send_reminder_date: '2024-01-20'
  }
};

// Example 4: Follow up status with reminder
const followUpStatus: StatusChangeRequest = {
  statusId: 4, // Assuming this is the ID for FOLLOW_UP
  reminder: {
    title: 'Follow up',
    content: 'Follow up reminder.',
    due_date: '2024-01-25',
    priority: 'low',
    remarks: 'Check client interest',
    send_email: false,
    send_reminder_date: '2024-01-25'
  }
};

// Function to test the API calls
async function testStatusUpdates() {
  const leadId = 123; // Replace with actual lead ID

  try {
    console.log('Testing simple status change...');
    await updateLeadStatus(leadId, simpleStatusChange);
    console.log('✅ Simple status change successful');

    console.log('Testing meeting scheduled status...');
    await updateLeadStatus(leadId, meetingScheduledStatus);
    console.log('✅ Meeting scheduled status successful');

    console.log('Testing viewing scheduled status...');
    await updateLeadStatus(leadId, viewingScheduledStatus);
    console.log('✅ Viewing scheduled status successful');

    console.log('Testing follow up status...');
    await updateLeadStatus(leadId, followUpStatus);
    console.log('✅ Follow up status successful');

  } catch (error) {
    console.error('❌ Status update failed:', error);
  }
}

// Export for use in other files
export {
  simpleStatusChange,
  meetingScheduledStatus,
  viewingScheduledStatus,
  followUpStatus,
  testStatusUpdates
};

// Usage notes:
// 1. The backend expects statusId as the main identifier
// 2. For MEETING_SCHEDULED and VIEWING_SCHEDULED, reminder object is required
// 3. For VIEWING_SCHEDULED, listing_ids array is required
// 4. For other statuses, remark field is required
// 5. Date format should be YYYY-MM-DD for the API
// 6. Priority should be 'low', 'medium', or 'high'
