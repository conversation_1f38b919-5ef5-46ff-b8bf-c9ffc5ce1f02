<?php

// Creează această migrație pentru a stoca programările de viewing
// php artisan make:migration create_lead_viewing_schedules_table

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('lead_viewing_schedules', function (Blueprint $table) {
            $table->id();
            $table->foreignId('lead_id')->constrained('leads')->onDelete('cascade');
            $table->foreignId('listing_id')->constrained('listings')->onDelete('cascade');
            $table->timestamp('scheduled_at')->nullable(); // Când e programată vizionarea
            $table->enum('status', ['scheduled', 'completed', 'cancelled'])->default('scheduled');
            $table->text('notes')->nullable(); // Note despre vizionare
            $table->timestamps();
            
            // Index pentru căutări rapide
            $table->index(['lead_id', 'status']);
            $table->index(['listing_id', 'scheduled_at']);
            
            // Previne duplicate
            $table->unique(['lead_id', 'listing_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('lead_viewing_schedules');
    }
};

// ALTERNATIV: Dacă preferi să adaugi o coloană JSON în tabela leads
// Adaugă această migrație pentru a modifica tabela leads:

/*
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('leads', function (Blueprint $table) {
            $table->json('viewing_properties')->nullable()->after('lead_status_id');
            $table->timestamp('viewing_scheduled_at')->nullable()->after('viewing_properties');
        });
    }

    public function down(): void
    {
        Schema::table('leads', function (Blueprint $table) {
            $table->dropColumn(['viewing_properties', 'viewing_scheduled_at']);
        });
    }
};
*/
