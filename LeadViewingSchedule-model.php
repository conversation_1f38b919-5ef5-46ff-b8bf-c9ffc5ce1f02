<?php

// app/Models/LeadViewingSchedule.php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class LeadViewingSchedule extends Model
{
    use HasFactory;

    protected $fillable = [
        'lead_id',
        'listing_id',
        'scheduled_at',
        'status',
        'notes',
    ];

    protected $casts = [
        'scheduled_at' => 'datetime',
    ];

    /**
     * Get the lead that owns the viewing schedule.
     */
    public function lead(): BelongsTo
    {
        return $this->belongsTo(Lead::class);
    }

    /**
     * Get the listing for this viewing schedule.
     */
    public function listing(): BelongsTo
    {
        return $this->belongsTo(Listing::class);
    }

    /**
     * Scope pentru programări active
     */
    public function scopeScheduled($query)
    {
        return $query->where('status', 'scheduled');
    }

    /**
     * Scope pentru programări completate
     */
    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    /**
     * Scope pentru programări anulate
     */
    public function scopeCancelled($query)
    {
        return $query->where('status', 'cancelled');
    }
}

// Adaugă și în modelul Lead.php aceste relații:

/*
// În app/Models/Lead.php

public function viewingSchedules()
{
    return $this->hasMany(LeadViewingSchedule::class);
}

public function scheduledViewings()
{
    return $this->viewingSchedules()->scheduled();
}

public function viewingListings()
{
    return $this->belongsToManyThrough(
        Listing::class,
        LeadViewingSchedule::class,
        'lead_id',
        'id',
        'id',
        'listing_id'
    )->where('lead_viewing_schedules.status', 'scheduled');
}
*/
