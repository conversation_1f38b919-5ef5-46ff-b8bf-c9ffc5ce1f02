# ✅ OFFER NEGOTIATION - PAYLOAD FINAL CORECT

## 🎯 Structura pentru Offer Negotiation

Pentru Offer Negotiation (status 16), <PERSON><PERSON> așteaptă același format ca pentru statusurile complexe (14, 15, 23) cu `viewingScheduledData`.

## 📋 Logica implementată

### **Din codul Laravel:**
```php
if ($validData['status'] == '14' || $validData['status'] == '15' || $validData['status'] == '23' || $validData['status'] == '16') {
    // VIEWING_SCHEDULED && FOLLOW_UP && MEETING_SCHEDULED && OFFER_NEGOTIATION
    
    if (isset($validData['viewingScheduledData']['reminder'])) {
        // Creează reminder
    }
    
    // Creează proposals pentru proprietăți
    foreach ($validData['viewingScheduledData']['listingSelection'] as $listingId => $listingRefNo) {
        $newProposal = new LeadProposal();
        $newProposal->lead_id = $lead->id;
        $newProposal->property_id = $listingId;
        $newProposal->save();
    }
}
```

### **Frontend implementat:**
```typescript
// Handle Offer Negotiation status
else if (selectedStatusObj.name === 'OFFER_NEGOTIATION' && config) {
  // Creează listingSelection pentru offer negotiation
  let listingSelection: { [key: string]: string } = {};
  if (config.selectedProperties && config.selectedProperties.length > 0) {
    config.selectedProperties.forEach((propertyId: string) => {
      const property = listings.find(listing => listing.id.toString() === propertyId);
      if (property) {
        listingSelection[propertyId] = property.ref_no;
      }
    });
  }

  payload.viewingScheduledData = {
    reminder: {
      title: 'Offer Negotiation',
      content: 'Offer negotiation started.',
      priority: 'medium',
      dueDate: undefined,
      sendEmail: false,
      sendReminderDate: undefined,
    },
    listingSelection: listingSelection,
    remarks: 'Offer negotiation started',
  };
  // Adaugă remarks și la nivel de root pentru validare
  payload.remarks = 'Offer negotiation started';
}
```

## 📊 Payload pentru Offer Negotiation

### **Când selectezi proprietăți:**
```json
{
  "status": 16,
  "viewingScheduledData": {
    "reminder": {
      "title": "Offer Negotiation",
      "content": "Offer negotiation started.",
      "priority": "medium",
      "dueDate": null,
      "sendEmail": false,
      "sendReminderDate": null
    },
    "listingSelection": {
      "11615": "AS-002007-3751",
      "11616": "AS-002008-3751",
      "11614": "OR-000653-3962"
    },
    "remarks": "Offer negotiation started"
  },
  "remarks": "Offer negotiation started"
}
```

### **Când nu selectezi proprietăți:**
```json
{
  "status": 16,
  "viewingScheduledData": {
    "reminder": {
      "title": "Offer Negotiation",
      "content": "Offer negotiation started.",
      "priority": "medium",
      "dueDate": null,
      "sendEmail": false,
      "sendReminderDate": null
    },
    "listingSelection": {},
    "remarks": "Offer negotiation started"
  },
  "remarks": "Offer negotiation started"
}
```

## 🔧 Ce face Laravel cu payload-ul

### **1. Validare:**
```php
$validData = request()->validate([
    'status' => 'exists:lead_status,id',  // ✅ Status 16 valid
    'remarks' => 'required_unless:status,14,15,23,26',  // ✅ Nu e necesar pentru 16
    'viewingScheduledData' => 'required_if:status,14',  // ✅ Nu e necesar pentru 16
]);
```

### **2. Procesare reminder:**
```php
if (isset($validData['viewingScheduledData']['reminder'])) {
    $reminderData = [
        'title' => 'Offer Negotiation',
        'text' => 'Offer negotiation started.',
        'priority' => 'medium',
        'due_date' => null,
        'reminder_email' => false,
        'reminder_email_date' => null,
        'reminder_type' => 'OFFER_NEGOTIATION',
    ];
    $this->notesService->createReminderForLead($lead, $reminderData);
}
```

### **3. Procesare proposals:**
```php
foreach ($validData['viewingScheduledData']['listingSelection'] as $listingId => $listingRefNo) {
    $listingAlreadyProposed = false;
    foreach ($lead->proposals as $proposal) {
        if ($proposal->property->id == $listingId) {
            $listingAlreadyProposed = true;
        }
    }
    if (!$listingAlreadyProposed) {
        $newProposal = new LeadProposal();
        $newProposal->lead_id = $lead->id;
        $newProposal->property_id = $listingId;
        $newProposal->save();
    }
}
```

### **4. Operation history:**
```php
$remarksBody = "Lead status updated from [Old] to [OFFER_NEGOTIATION]";
if (!empty($validData['remarks'])) {
    $remarksBody .= "\r\nRemarks:\r\n" . $validData['remarks'];
}
if (!empty($reminderRemarks)) {
    $remarksBody .= "\r\nReminder remarks:\r\n " . $reminderRemarks;
}
```

## ✅ Beneficii

### **1. Proposals automate:**
- ✅ **Proprietățile selectate** se adaugă automat ca proposals la lead
- ✅ **Fără duplicate** - verifică dacă proprietatea e deja propusă
- ✅ **Tracking complet** - toate proprietățile sunt legate de lead

### **2. Reminder creat:**
- ✅ **Reminder automat** pentru offer negotiation
- ✅ **Detalii complete** - title, content, priority
- ✅ **Tip specific** - reminder_type = 'OFFER_NEGOTIATION'

### **3. Operation history:**
- ✅ **Schimbarea statusului** este înregistrată
- ✅ **Remarks incluse** - atât generale cât și de reminder
- ✅ **Audit trail** complet pentru offer negotiation

## 🔍 Cum să testezi

### **Pasul 1: Selectează Offer Negotiation**
1. Alege status "Offer Negotiation"
2. Selectează 2-3 proprietăți din listă
3. Apasă "Save Status"

### **Pasul 2: Verifică payload-ul**
În console, verifică că se trimite:
```json
{
  "status": 16,
  "viewingScheduledData": {
    "listingSelection": {
      "11615": "AS-002007-3751",
      "11616": "AS-002008-3751"
    }
  }
}
```

### **Pasul 3: Verifică rezultatele**
1. **Status schimbat** - lead-ul are status "Offer Negotiation"
2. **Proposals create** - proprietățile apar în proposals
3. **Reminder creat** - reminder pentru offer negotiation
4. **Operation history** - schimbarea e înregistrată

## 📋 Comparație cu alte statusuri

| Status | Payload | Proposals | Reminder | Email |
|--------|---------|-----------|----------|-------|
| 14 (Viewing) | `viewingScheduledData` + `listing_ids` | ✅ | ✅ | ✅ |
| 15 (Follow Up) | `viewingScheduledData` | ✅ | ✅ | ❌ |
| 23 (Meeting) | `viewingScheduledData` | ✅ | ✅ | ❌ |
| **16 (Offer)** | **`viewingScheduledData`** | **✅** | **✅** | **❌** |

## 🚀 Rezultat final

**Offer Negotiation funcționează acum ca un status complex:**

- ✅ **Selectare proprietăți** - prin OfferNegotiationForm
- ✅ **Payload corect** - cu viewingScheduledData și listingSelection
- ✅ **Proposals automate** - proprietățile se adaugă la lead
- ✅ **Reminder creat** - pentru tracking offer negotiation
- ✅ **Operation history** - schimbarea e înregistrată complet

**Offer Negotiation are acum funcționalitate completă cu proposals și tracking!** 🎉
