# 🔄 IMPLEMENTARE PENTRU NOUA STRUCTURĂ LARAVEL

## 📋 Noua structură Laravel identificată

Laravel folosește acum o structură complet diferită cu două funcții:

### 1. **statusChange** (controller principal)
```php
public function statusChange($leadId){
    $response = Http::post(route('update-lead-status', ['id', $leadId]), [
        'agent' => $agent,
        'status' => request()->get('statusId'),
        'remarks' => request()->get('remarks'),
        'viewingScheduledData' => ['reminder' => request()->get('reminder')],
    ]);
}
```

### 2. **updateLeadStatus** (funcția care procesează datele)
```php
$validData = request()->validate([
    'agent' => 'required_if:status,21',
    'status' => 'exists:lead_status,id',
    'remarks' => 'required_unless:status,14,15,23,26',
    'viewingScheduledData' => 'required_if:status,14',
]);
```

## ✅ Actualizări făcute în Frontend

### 1. **Interfața TypeScript actualizată**
```typescript
export interface StatusChangeRequest {
  status: number;                    // era 'statusId'
  agent?: number;                    // nou
  remarks?: string;                  // era 'remark'
  viewingScheduledData?: {           // nou - pentru statusuri complexe
    reminder: {
      title?: string;
      content?: string;              // era 'text'
      priority?: 'low' | 'medium' | 'high';
      dueDate?: string;              // era 'due_date'
      sendEmail?: boolean;           // era 'reminder_email'
      sendReminderDate?: string;     // era 'reminder_email_date'
    };
    listingSelection?: { [key: string]: string };  // nou
    remarks?: string;
  };
}
```

### 2. **Payload-uri actualizate**

#### **Statusuri complexe (Meeting, Viewing, Follow-up):**
```typescript
payload.viewingScheduledData = {
  reminder: {
    title: config.title || 'Meeting appointment',
    content: config.content || 'Meeting appointment reminder.',
    priority: config.priority?.toLowerCase() || 'low',
    dueDate: config.dueDate ? formatDateForAPI(config.dueDate) : undefined,
    sendEmail: config.sendEmail || false,
    sendReminderDate: config.dueDate ? formatDateForAPI(config.dueDate) : undefined,
  },
  listingSelection: listingSelection,  // pentru viewing scheduled
  remarks: config.remarks || '',
};
```

#### **Statusuri simple:**
```typescript
payload.remarks = config?.remarks || `Status changed to ${statusName}`;
```

## 🎯 Payload-uri finale trimise către Laravel

### **Meeting Scheduled (status: 23):**
```json
{
  "status": 23,
  "agent": 123,
  "viewingScheduledData": {
    "reminder": {
      "title": "Meeting with John Doe",
      "content": "Discuss property requirements",
      "priority": "high",
      "dueDate": "2024-01-15",
      "sendEmail": true,
      "sendReminderDate": "2024-01-15"
    },
    "listingSelection": {},
    "remarks": "Important client meeting"
  }
}
```

### **Viewing Scheduled (status: 14):**
```json
{
  "status": 14,
  "agent": 123,
  "viewingScheduledData": {
    "reminder": {
      "title": "Viewing appointment",
      "content": "Property viewing reminder",
      "priority": "high",
      "dueDate": "2024-01-20",
      "sendEmail": true,
      "sendReminderDate": "2024-01-20"
    },
    "listingSelection": {
      "101": "REF001",
      "102": "REF002",
      "103": "REF003"
    },
    "remarks": "Show premium properties"
  }
}
```

### **Follow Up (status: 15):**
```json
{
  "status": 15,
  "agent": 123,
  "viewingScheduledData": {
    "reminder": {
      "title": "Follow up",
      "content": "Follow up reminder",
      "priority": "low",
      "dueDate": "2024-01-25",
      "sendEmail": false,
      "sendReminderDate": "2024-01-25"
    },
    "listingSelection": {},
    "remarks": "Check client interest"
  }
}
```

### **Status simplu (ex: Contacted):**
```json
{
  "status": 1,
  "agent": 123,
  "remarks": "Client is very interested in 2-bedroom apartments"
}
```

## 🔧 Ce face Laravel cu aceste date

### **Pentru statusuri complexe (14, 15, 23, 16):**
1. **Creează reminder** cu datele din `viewingScheduledData.reminder`
2. **Pentru viewing scheduled** - creează proposals pentru fiecare proprietate din `listingSelection`
3. **Trimite email-uri** pentru viewing scheduled (owner + client notifications)
4. **Adaugă în operation history** cu remarks-urile

### **Pentru statusuri simple:**
1. **Adaugă în operation history** cu remarks-ul
2. **Actualizează statusul** lead-ului

### **Logica specială:**
- **Status 21** - necesită agent assignment
- **Status 26** - creează reminder automat pentru 3 luni
- **Status 14** - trimite notificări email către owner și client

## ✅ Rezultat final

**Frontend-ul este acum complet sincronizat cu noua structură Laravel!**

- ✅ **Payload-uri corecte** - trimite exact ce așteaptă Laravel
- ✅ **Structură nouă** - folosește `viewingScheduledData` pentru statusuri complexe
- ✅ **listingSelection** - convertește array-ul de IDs în obiect cu ref_no
- ✅ **Validare corectă** - respectă validările Laravel
- ✅ **TypeScript actualizat** - interfețele reflectă structura corectă

**Acum schimbarea de status va funcționa perfect cu noua arhitectură Laravel!** 🚀
