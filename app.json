{"expo": {"name": "Fgrealty Agent App", "slug": "comfgrealtyappagent", "orientation": "portrait", "icon": "assets/images/SymbolGold.png", "splash": {"image": "assets/images/SymbolGold.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "userInterfaceStyle": "light", "version": "1.0.31", "scheme": "myapp", "newArchEnabled": true, "ios": {"supportsTablet": true, "bundleIdentifier": "com.fgrealty.app.agent", "buildNumber": "3", "infoPlist": {"ITSAppUsesNonExemptEncryption": false, "NSLocationWhenInUseUsageDescription": "This app needs access to location to show your position on the map.", "NSLocationAlwaysUsageDescription": "This app needs access to location to show your position on the map.", "UIBackgroundModes": ["location"], "LSApplicationQueriesSchemes": ["whatsapp", "tel", "mailto"]}}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/SymbolGold.png", "backgroundColor": "#ffffff"}, "versionCode": 3, "package": "com.fgrealty.app.agent", "config": {"googleMaps": {"apiKey": "AIzaSyDWceQFkg4sCUxM3pcfpKSh3IPubb14DY8"}}, "permissions": ["android.permission.RECORD_AUDIO", "android.permission.POST_NOTIFICATIONS", "android.permission.RECEIVE_BOOT_COMPLETED", "android.permission.SCHEDULE_EXACT_ALARM", "android.permission.USE_EXACT_ALARM"], "googleServicesFile": "./google-services.json", "intentFilters": [{"action": "VIEW", "data": [{"scheme": "https", "host": "wa.me"}], "category": ["BROWSABLE", "DEFAULT"]}], "queries": [{"package": "com.whatsapp"}, {"package": "com.whatsapp.w4b"}]}, "web": {"bundler": "metro", "output": "single", "favicon": "./assets/images/favicon.png"}, "plugins": ["expo-router", ["expo-image-picker", {"photosPermission": "Allow access to your photos to upload property listings, capture property images, and set your professional profile picture. This helps create engaging listings and maintain a professional presence on the platform."}], ["expo-notifications", {"icon": "./assets/images/SymbolGold.png", "color": "#ffffff"}], "expo-secure-store", ["expo-location", {"locationAlwaysAndWhenInUsePermission": "Allow $(PRODUCT_NAME) to use your location."}]], "experiments": {"typedRoutes": true}, "extra": {"router": {"origin": false}, "eas": {"projectId": "e4d322ee-2883-4ad6-bd33-9cd91ce6e918"}}, "owner": "fgrealty"}}